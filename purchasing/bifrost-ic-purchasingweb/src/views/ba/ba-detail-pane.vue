<template>
  <div class="baDetailTabContainer" style="height: 100%">
    <el-tabs
      v-model="activeTab"
      @tab-click="tabClick"
      class="baDetailTabs"
      >
      <el-tab-pane :key="tab.name" :label="tab.label" :name="tab.name" v-for="tab in tabs">
            <keep-alive>
                <component ref="tabComponents" :is="tab.name"/>
            </keep-alive>
        </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import $ from 'jquery'
export default {
  name: 'ba-detail-pane',
  data() {
    return {
      activeTab: '',
      ids: '',
      tabs: [],
      dlg: {},
      dynamicTabs: [],
      tabName: ''
    }
  },
  methods: {
    init(params, dlg) {
      $('.baDetailTabs').find('div:first').css({
        display: 'block'
      })
      let isBtPlan = false // 是否有 月度/季度计划 按钮
      params.buttons && params.buttons.forEach((bt) => {
        if (bt.text === '月度/季度计划') {
          isBtPlan = true
        }
      })
      const param = {}
      param.billId = this.ids

      if (this.$isNotEmpty(params.fileName)) {
        var prefix = `formDetail${params.fileName}-`
        var names = window.$viewNames
        var keys = Object.keys(names)
        keys.forEach(name => {
          if (name.indexOf(prefix) === 0) { // 满足以formDetail${formType}-开头
            // 没有 月度/季度计划 按钮 则不显示执行计划tab
            if (!isBtPlan && name.indexOf('执行计划') > -1) {
              return
            }
            if (this.$isNotEmpty(params.hideDetailTabs)) {
              const label = this.$resolveTabTitle(name)
              if (params.hideDetailTabs.includes(label)) {
                return
              }
            }
            this.dynamicTabs.push(name)
          }
        })
      }

      if (this.$isNotEmpty(this.dynamicTabs)) {
        this.dynamicTabs = [...new Set(this.dynamicTabs)]
        this.tabs = []
        this.tabsIndex = {}
        this.dlg = dlg
        this.params = params
        for (var i = 0; i < this.dynamicTabs.length; i++) {
          var tabStr = this.dynamicTabs[i]
          var label = this.$resolveTabTitle(tabStr)
          this.tabs.push({ 'name': tabStr, 'label': label })
          this.tabsIndex[tabStr] = i
        }

        // this.tabs.forEach((value, index, array) => {
        //   if (value.label === '详情') {
        //     array.splice(value, 1)
        //   }
        // })

        this.$nextTick(() => {
          let tabIndex = this.tabs.findIndex(item => item.name === this.activeTab)
          if (tabIndex === -1) {
            tabIndex = 0
          }
          this.tabClick(this.tabs[tabIndex])
          this.activeTab = this.tabs[tabIndex].name
        })
      }
    },
    mainTableSelectRowChanged(rows) {
      this.ids = this.$getTableCheckedIdsStrBy(rows)
      this.init(this.ids)
    },
    tabClick(tab) {
      var tabName = tab.name
      var index = this.tabsIndex[tabName]
      this.tabName = tab.label
      this.$refs.tabComponents[index].init(this.dlg, this.params)
    }
  }
}
</script>

<style lang="scss">
.baDetailTabContainer .baDetailTabs .el-tabs__header {
  margin: -5px 0px 0px 0px;
}
.baDetailTabs .el-tabs__nav-wrap::after {
  height: 1px;
  background-color: #fff;
}
.baDetailTabs .el-tabs__item {
  height: 32px !important;
  line-height: 34px !important;
}
.baDetailTabs .el-tabs__active-bar.is-top{
  width: 59px !important;
}
</style>
