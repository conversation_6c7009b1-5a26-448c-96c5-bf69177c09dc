<template>
  <div id="LeftTop">
    <dv-border-box-13>
      <header-Title title="经费卡管理" />
      <left-TopEchart />
    </dv-border-box-13>
  </div>
</template>

<script>
import LeftTopEchart from '@/views/business/dataBigScreen/components/echart/left/left-top-echart/index.vue'
import headerTitle from '@/views/business/dataBigScreen/components/header-title/header-title.vue'
export default {
  name: 'LeftTop',
  components: {
    LeftTopEchart,
    headerTitle
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {},
}
</script>
<style scoped lang="scss">
#LeftTop {
  width: 100%;
  height: 310px;
}
</style>
