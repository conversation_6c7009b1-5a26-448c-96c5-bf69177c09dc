<!--
 * @Description:
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-04-16 10:32:03
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-08-28 17:02:23
-->
<template>
  <div class="containerWrapper">
    <div class="top-section">
      <!-- 第一行：按钮 -->
      <div class="button-section" v-if="isCustomBtn">
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-document-add"
          @click="handleAdd"
          >添加</el-button
        >
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-edit"
          @click="handleEdit"
          :disabled="!treeId"
          >修改</el-button
        >
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-delete"
          @click="handleDelete"
          :disabled="!treeId"
          >删除</el-button
        >
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-view"
          @click="handlePreview"
          >合并预览</el-button
        >
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-back"
          @click="handleBack"
          >返回</el-button
        >
      </div>
      <div class="button-section" v-else>
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-document-checked"
          @click="handleCustomSure"
          :disabled="!treeId"
          >保存</el-button
        >
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-printer"
          @click="handleNewFile"
          :disabled="!treeId"
          >生成PDF</el-button
        >
      </div>
    </div>
    <div class="main-section">
      <!-- 左侧树形菜单 -->
      <div class="tree-section">
        <el-input
          placeholder="输入关键字进行过滤"
          v-model="filterText"
        ></el-input>
        <el-tree
          :data="treeData"
          node-key="id"
          :expand-on-click-node="false"
          highlight-current
          @node-click="handleNodeClick"
          :default-expand-all="expandAll"
          :props="{ label: 'catalogName', children: 'children' }"
          :filter-node-method="filterNode"
          ref="tree"
          style="margin-top: 10px"
        >
          <template #default="{ node, data }">
              <span class="custom-node">
                <i class="red-dot" v-if="moduleType === '编制采购文件' && data.needRevise === '是'"></i>
                {{ node.label }}
              </span>
          </template>
          </el-tree>
      </div>
      <!-- 右侧表格 -->
      <div class="table-section" v-if="showContent">
        <p class="preview-text" v-if="emptText">暂无数据</p>
        <!-- 根据条件展示 富文本/表格 -->
        <!--  -->
        <div style="width: 100%;overflow:hidden">
          <div class="header" v-if="isCustomBtn">
            <div class="keyWord-conetent">
              <el-select v-model="targetValue" placeholder="请选择要评审报告类型" @change="changeTarget">
                  <el-option
                    v-for="item in options"
                    :key="item.name + item.type"
                    :label="item.name"
                    :value="item.name">
                  </el-option>
                </el-select>
                <el-button
                class="pur-button-list custom-button-sure"
                icon="el-icon-document-add"
                @click="handleSure"
                type="primary"
                style="margin-left: 10px"
                :disabled="isContentDisabled"
                >保存</el-button
              >
            </div>
          </div>
          <div v-if="isCustomBtn && targetValue === '富文本框自由编辑'" style="padding: 0px 10px;">
            <el-button 
             v-for="item in targetList"
             @click="targetClick(item)"
             :key="item.name"
             style="margin-right: 10px;margin-bottom: 10px; margin-left:0;">
             {{item.name}}
            </el-button>
          </div>
          <myTinymce
            :editHeight="!isCustomBtn === false ? '550' : '600'"
            :style="{ height: !isCustomBtn === false ? 'calc(80vh - 0px)' : '600px' }"
            v-model="tinymceContent"
            :isDisabled="targetValue !== '富文本框自由编辑'"
            ref="tinymceRef"
            v-if="showContent"
          />
        </div>
      </div>
    </div>
    <moduleChange ref="moduleChange" @init="refresh"></moduleChange>
    <mergeView ref="mergeView"
    ></mergeView>
    <wordImp
      ref="wordImp"
      @init="handleNodeClick(treeObj)"
      :treeId="treeId"
    ></wordImp>
    <viewTemplate
    ref="viewTemplate"
    @changeContent="changeContent"
    :isCustomBtn="isCustomBtn"
    :isAudit="isAudit"
    :isSupplier="isSupplier"
    ></viewTemplate>
  </div>
</template>

<script>
import moduleChange from "./moduleChange.vue";
import MyTinymce from "@/components/tinymce/myTinymce";
import mergeView from "./mergeView.vue";
import wordImp from "./wordImp.vue";
import viewTemplate from "./viewTemplate.vue";
export default {
  components: {
    moduleChange,
    MyTinymce,
    mergeView,
    wordImp,
    viewTemplate,
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  props: {
    isCustomBtn: {
      type: Boolean,
      default: true,
    },
    // 是否为自定义模块
    projectInfoBizid: {
      type: String,
      default: "",
    },
    // 项目id
    projectInfoStatus: {
      type: String,
      default: "",
    },
    // 项目状态
    isAudit: {
      type: Boolean,
      default: false,
    },
    isSupplier:{
      type: Boolean,
      default: false
    },
    // 是否供应商
    moduleType: {
       type: String,
       default: ""
    },
    // 模块类型
    projectStatus: {
      type: String,
      default: "",
    },
    // 项目状态
    isDetail: {
      type: Boolean,
      default: false,
    },
    // 是否详情
    crateFileText: {
      type: String,
      default: "生成文件"
    },
    module:{
      type: String,
      default: ""
    }
  },
  computed: {
    isContentDisabled() {
      return  !this.treeId || !this.treeObj.bizid || !this.targetValue;
    }
  },
  data() {
    return {
      title: "配置模版",
      dialogVisible: false,
      tinymceContent: "",
      filterText: "",
      treeData: [],
      // 树形菜单数据
      defaultExpand: [],
      templatebizid: "",
      treeId: "",
      treeObj: {},
      contentType: "",
      emptText: true,
      isEdit: false,
      isSaved: false,
      showContent: true, //表格/富文本
      tableData: [],
      loading: false,
      rowIndex: 0,
      randNum: 1 + Math.random(),
      isSLing: false,
      totalAmountPrefixMark: "",
      ifEdit: false,
      annotate: "",
      isEditBtn: '',
      expandAll: true,
      options: [],
      discountType: "",
      targetValue: "",
      targetText: "",
      targetList: [],
      isReadOnlyId: false,
      isHotst: false,
    };
  },
  methods: {
    init(templatebizid, type = false) {
      if (templatebizid) {
        this.templatebizid = templatebizid;
      }
      this.isHotst = type
      this.contentType = "";
      this.emptText = true
      let fetchApi
      let obj = {}
      if(this.isHotst) {
        fetchApi = "treeReviewReportContent"
        obj = { 
          ids: this.templatebizid,
        }
      }else {
        fetchApi = "treeReviewReportTemplateCatalog"
        obj = { templatebizid: this.templatebizid }
      }
      this.$callApiParams(`${fetchApi}`, obj, (result) => {
        this.treeData = result.data;
        if(!this.isReadOnlyId) {
          this.treeId = "";
        }
        return true;
      });
    },
    // 操作刷新选中树
    refresh(id) {
      this.treeId = id;
      this.init()
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(id)
      })
    },
    // 投标报名生成文件
    handleNewFile() {
      this.$callApiParams(`saveReviewReportPDF`, {databizid: this.templatebizid}, (result) => {
        this.$message.success("生成文件成功")
        return true;
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleBack() {
      this.$emit("handleBack");
    },
    handleAdd() {
      this.$refs.moduleChange.handleOpen(
        "add",
        this.treeData,
        this.templatebizid,
        this.treeObj
      );
    },
    handleEdit() {
      // 处理修改逻辑
      if (!this.treeId) {
        this.$message.warning("请选择要修改的节点");
        return;
      }
      this.$refs.moduleChange.handleOpen(
        "edit",
        this.treeData,
        this.templatebizid,
        this.treeObj
      );
    },
    handleDelete() {
      // 处理删除逻辑
      if (!this.treeId) {
        this.$message.warning("请选择要删除的节点");
        return;
      }

      this.$confirm("确定删除选中的项目?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$callApi(
          "deleteReviewReportTemplateCatalog&ids=" + this.treeId,
          {},
          (result) => {
            this.init();
            this.treeId = "";
          }
        );
      });
    },
    handlePreview() {
      // 处理合并预览逻辑
      let bizid
      if(!this.isCustomBtn) {
        bizid =  this.projectInfoBizid
      } else {
        bizid = this.templatebizid
      }

      this.$refs.viewTemplate.handleOpen(bizid, '合并预览');
    },
    handleNodeClick(node) {
      console.log("handleNodeClick", node);
      this.isReadOnlyId = true
      this.tableData = [];
      this.totalAmountPrefixMark = "";
      this.isEditBtn = node?.ifEdit
      this.treeId = node?.id;
      this.treeObj = node;
      this.contentType = node?.contentType;
      this.isSLing = false;
      this.targetValue = ''
      this.tinymceContent = ''
      this.tinymceContent = node?.content
      this.$refs.tinymceRef.setInitVal(this.tinymceContent);
      this.targetValue = node?.type
    },
    changeTarget(val) {
      console.log(val)
      // 查找选中的项以获取content
      const selectedItem = this.options.find(item => item.name === val);
      if (selectedItem) {
        
        if(selectedItem.name === '富文本框自由编辑') {
          this.tinymceContent = this.treeObj?.content || selectedItem?.content;
        }else {
          this.tinymceContent = selectedItem.content;
        }
        this.$refs.tinymceRef.setInitVal(this.tinymceContent);
      } else if (!val) {
        // 当清空选择时，清空内容
        this.tinymceContent = '';
        this.$refs.tinymceRef.setInitVal(this.tinymceContent);
      }
    },
    targetClick(val) {
      if(!this.targetValue) {
        this.$message.error('请选择要评审报告类型！')
        return
      }
      
      // 构建要检查的模式
      const pattern = `${val.name}: ${val.content}`;
      
      // 转义特殊字符以构建正则表达式
      const escapedPattern = pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      
      // 使用正则表达式检查是否已存在
      const regex = new RegExp(escapedPattern);
      
      // 如果当前内容中不包含要插入的内容，则插入
      if (!regex.test(this.tinymceContent)) {
        // 在末尾添加新内容，保留原有内容
        if (this.tinymceContent) {
          this.tinymceContent += `<br>${pattern}`;
        } else {
          this.tinymceContent = pattern;
        }
        
        // 更新富文本编辑器内容
        this.$refs.tinymceRef.setInitVal(this.tinymceContent);
      } else {
        console.log('内容已存在，无需重复插入');
      }
    },
    getOntentList() {
      return new Promise((resolve, reject) => { 
        this.$callApiParams(
          "reviewReportTemplateContentList",
          {},
          (result) => {
            this.options = result?.data;
            resolve();
            return true;
          }
        );
      });
    },
    getTargetList() {
      return new Promise((resolve, reject) => {
        this.$callApiParams(
          "reviewReportTemplateList",
          { bizid: this.treeId },
          (result) => {
            this.targetList = result?.data;
            resolve();
            return true;
          }
        );
      })

    },
    // 获取所有表格元素添加样式
    addTableBordersIfMissing(htmlString) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlString, 'text/html');
      const tables = doc.getElementsByTagName('table');

      for (let i = 0; i < tables.length; i++) {
        const table = tables[i];
        if (!table.getAttribute('border')) {
          table.setAttribute('border', '1');
        }
        table.setAttribute('style', 'width: 100%; border-collapse: collapse;');
        const tds = table.getElementsByTagName('td');
        for (let j = 0; j < tds.length; j++) {
          const td = tds[j];
          const spans = td.getElementsByTagName('span');
          if (spans.length > 0) {
            for (let k = 0; k < spans.length; k++) {
              td.textContent = td.textContent.replace(spans[k], spans[k].textContent); // 移除span并替换为文本内容
              td.textContent = td.textContent.replace(/\u00A0/g, ''); // 移除td中的&nbsp;
            }
          } else {
            const textNodes = td.childNodes;
            for (let n = 0; n < textNodes.length; n++) {
              if (textNodes[n].nodeType === Node.TEXT_NODE) {
                textNodes[n].nodeValue = textNodes[n].nodeValue.replace(/\u00A0/g, ''); // 移除td中的&nbsp;
              }
            }
          }
        }
      }
      return doc.documentElement.innerHTML;
    },
    // 保存
    async handleSure(type) {
      this.isSLing = true;
      if(!this.treeObj){
        this.$message.error("请选择目录")
        return
      }
      await this.handleSureTemplate()
      this.isReadOnlyId = true
      this.init(this.templatebizid, false)  
         // 保存当前选中的节点ID
      const selectedNodeId = this.treeId;
        // 使用多种方式确保选中状态被正确设置
        this.$nextTick(() => {
          setTimeout(() => {
            if (this.$refs.tree && selectedNodeId) {
              this.$refs.tree.setCurrentKey(selectedNodeId)
               // 手动更新treeObj数据
               this.updateTreeObj(selectedNodeId);
            }
          }, 200);
        });
    },
    handleSureTemplate() { 
     return new Promise((resolve, reject) => {
      let obj = {};
      let fetchApi;
      const  { catalogName,parentId, sort, bizid } = this.treeObj
      if(this.isHotst) {
        fetchApi = "saveReviewReportContent"
        obj = { 
          bizid,
          catalogName,
          parentId,
          sort,
          content: this.tinymceContent
        }
        if(!bizid) {
          this.$message.error('请先选择目录再进行保存操作！')
          return
        }
      } else {
        fetchApi = "saveReviewReportTemplateCatalog"
        obj = {
          catalogName,
          parentId,
          sort,
          bizid,
          content: this.tinymceContent,
          templateBizid: this.templatebizid,
          type: this.targetValue
        }
      }
      this.$callApi(
        fetchApi,
        obj,
        (result) => {
          resolve(result)
          this.$message.success("保存成功")
          return true
        }
      );
    })
    },
    // 添加新方法：根据节点ID更新treeObj数据
    updateTreeObj(nodeId) {
      // 在treeData中查找对应的节点数据
      const findNode = (nodes) => {
        for (let node of nodes) {
          if (node.id === nodeId) {
            return node;
          }
          if (node.children && node.children.length > 0) {
            const found = findNode(node.children);
            if (found) return found;
          }
        }
        return null;
      };
      
      const node = findNode(this.treeData);
      if (node) {
        this.treeObj = node;
        // 同时更新其他相关属性
        this.treeId = node.id;
        this.contentType = node.contentType;
        this.targetValue = node.type;
        this.tinymceContent = node.content || '';
        this.$refs.tinymceRef.setInitVal(this.tinymceContent);
      }
    },
    changeContent() {
      if (this.contentType === "表格") return;
      this.showContent = true;
      this.$nextTick(() => {
        this.$refs.tinymceRef.setInitVal(this.tinymceContent);
      });
    },
    handleCustomSure() {
      // todo
      this.handleSureTemplate()
      this.init(this.templatebizid, true) 
      // 保存当前选中的节点ID
        const selectedNodeId = this.treeId;
        // 使用多种方式确保选中状态被正确设置
        this.$nextTick(() => {
          setTimeout(() => {
            if (this.$refs.tree && selectedNodeId) {
              this.$refs.tree.setCurrentKey(selectedNodeId)
            }
          }, 200);
        });
    },
  },
  mounted() {
    this.$set(this, "targetValue", "")
    this.$set(this, "tinymceContent", "");
    if(!this.isHotst) {
      this.getOntentList();
      this.getTargetList()  
    }
  },
  beforeDestroy() {
    this.$set(this, "targetValue", "")
    this.$set(this, "tinymceContent", "");
    this.isReadOnlyId = false
  },
};
</script>

<style scoped lang="scss">
.containerWrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px; /* 整个布局内边距 */
}

.top-section {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.search-section {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.button-section {
  display: flex;
  .tags {
    margin-left: 12px;
    height: 30px;
    width: 100%;
    display: flex;
    height: 30px;
  }
  .custom-button {
    color: #409eff;
    border: 1px solid #409eff !important;
    background-color: #e8f4ff;
    font-weight: bold;
    font-size: 14px;
    padding: 0px 12px;
    line-height: 30px;
    height: 30px;
  }
}

.main-section {
  display: flex;
  flex: 1;
}

.tree-section {
  width: 360px;
  padding: 10px;
  border: 1px solid #ddd;
}

.table-section {
  //   flex: 1; /* 右侧表格占据剩余空间 */
  flex: 1 1 100%;
  border: 1px solid #ddd;
  margin-left: 20px;
  display: flex;
  height: 100%;
  overflow-x: hidden;
  position: relative;
  .preview-text {
    position: absolute;
    top: 50%;
    left: 50%;
  }
  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 12px;
    margin-bottom: 12px;
  }
}
.tree-section,
.table-section {
  overflow-y: auto; /* 添加垂直滚动条 */
  // min-height: 500px; /* 设置最大高度以防止内容过长 */
  height: calc(80vh - 30px);
}
.tree-section {
  .custom-node {
    position: relative;
    display: flex;
    align-items: center;
    .red-dot {
      width: 6px;
      height: 6px;
      background-color: red;
      border-radius: 50%;
      margin-right: 5px;
    }
  }
}
[class^="el-icon-"] {
  font-family: element-icons !important;
  speak: none;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: baseline;
  display: inline-block;
  -webkit-font-smoothing: antialiased;
}
::v-deep .el-tree-node__expand-icon {
  font-size: 20px !important;
}
.iconBtn {
  i {
    cursor: pointer;
    margin: 0 3px;
  }

  .addicon {
    font-size: 26px;
    color: #67c23a;
  }

  .delicon {
    font-size: 26px;
    color: #f56c6c;
  }

  .diricon {
    font-size: 22px;
    color: #409eff;
  }

  .editicon {
    font-size: 26px;
    color: #409eff;
  }
}
</style>
