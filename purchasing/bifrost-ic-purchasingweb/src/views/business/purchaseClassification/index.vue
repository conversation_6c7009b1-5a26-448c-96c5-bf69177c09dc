<template>
  <div>
    <b-curd ref="curdList" />
    <PurchaseEdit ref="editDlg" />
  </div>
</template>

<script>
import PurchaseEdit from "./purchase-edit.vue";

export default {
  name: "PurchaseClassification",
  components: { PurchaseEdit },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      var initParams = {};
      initParams.params = {
        dataApiKey: "selectPurchaseClassificationPageData",
        deleteApiKey: "deletePurchaseClassification",
      };
      initParams.btTexts = { 新增: "新增" }
      initParams.buttons = [];

      initParams.leftTreeApiKey = "selectPurchaseClassificationTree";
      initParams.addParamsLeftTreeNodeClick = (exParams, treeNode) => {
        exParams["ID_like"] = treeNode.id;
      };

      initParams.searchForm = [];
      initParams.isShowOrderNumber = true;
      initParams.editDlg = this.$refs.editDlg;
      initParams.callbackRowDblclick = () => { };
      this.$refs.curdList.init(initParams);
    },
  },
};
</script>
