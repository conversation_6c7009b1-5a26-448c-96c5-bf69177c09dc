<template>
  <div>
    <b-curd ref="curdList" />
    <ding-talk-edit ref='dingTalk' @init="init" :treeList="treeList"></ding-talk-edit>
    <email-edit ref='email' @init="init" :treeList="treeList"></email-edit>
    <enterprise-wechat-edit ref='enterprise' @init="init" :treeList="treeList"></enterprise-wechat-edit>
    <wechat-public-platform-edit ref='platform' @init="init" :treeList="treeList"></wechat-public-platform-edit>
  </div>
</template>

<script>
import DingTalkEdit from './ding-talk-edit.vue'
import EmailEdit from "./email-edit.vue"
import EnterpriseWechatEdit from "./enterprise-wechat-edit.vue"
import WechatPublicPlatformEdit from "./wechat-public-platform-edit.vue"
export default {
  name: "third-config",
  components: { DingTalkEdit, EmailEdit, EnterpriseWechatEdit, WechatPublicPlatformEdit },
  data() {
    return {
      treeList: [],
    }
  },
  mounted() {
    this.$callApi("selectThirdAppConfigTree", {}, res => {
      this.treeList = res.data.filter(i => i.isLeaf)
      return true
    })
    this.init()
  },
  methods: {
    init() {
      let initParams = {}
      initParams.params = {
        dataApiKey: 'selectThirdAppConfigPageData',
        deleteApiKey: 'deleteThirdAppConfig'
      }
      initParams.searchForm = [
        '单位编码:AGENCY_CODE_like:文本',
        '单位名称:AGENCY_NAME_like:文本',
        '配置名称:CONFIG_NAME_like:文本',
      ]
      initParams.leftTreeApiKey = 'selectThirdAppConfigTree'
      initParams.addParamsLeftTreeNodeClick = (exParams, treeNode) => {
        exParams['AGENCY_ID_eq'] = treeNode.id
      }
      initParams.isShowOrderNumber = true
      initParams.hideCurdButton = ["新增", "修改", "详情", "删除"]
      const vm = this
      initParams.buttons = [
        {
          text: "新增配置",
          icon: "el-icon-document-add",
          mainButton: true,
          dropDowns: [
            {
              text: '新增钉钉配置',
              icon: 'el-icon-edit',
              click: () => {
                vm.$refs.dingTalk.handleOpen("新增")
              }
            },
            {
              text: '新增邮件配置',
              icon: 'el-icon-edit',
              click: () => {
                vm.$refs.email.handleOpen("新增")
              }
            },
            {
              text: '新增企业微信配置',
              icon: 'el-icon-edit',
              click: () => {
                vm.$refs.enterprise.handleOpen("新增")
              }
            },
            {
              text: '新增微信公众号配置',
              icon: 'el-icon-edit',
              click: () => {
                vm.$refs.platform.handleOpen("新增")
              }
            },
          ]
        },
        {
          text: "修改配置",
          icon: "el-icon-edit",
          enabledType: "1",
          click: data => {
            const row = data.params.rows[0]
            const type = row.configType
            let ref
            if(type == 0)  ref = vm.$refs.dingTalk
            if(type == 1)  ref = vm.$refs.email
            if(type == 2)  ref = vm.$refs.enterprise
            if(type == 3)  ref = vm.$refs.platform
            this.$callApiParams("selectThirdAppConfig", {
              ids: row.bizid,
              type
            }, res => {
              ref.handleOpen("编辑", res.data)
              return true
            })
          },
        },
        {
          text: "删除配置",
          icon: "el-icon-delete",
          enabledType: "1",
          click: data => {
            const row = data.params.rows[0]
            this.$confirm("确定删除选中的项目?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
                this.$callApiParams("deleteThirdAppConfig", { ids: row.bizid }, () => {
                  this.init()
                })
            })
          },
        }
      ]
      initParams.rowCheckedCallback = (rows) => {}
      this.$refs.curdList.init(initParams)
    }
  },
}
</script>
