<template>
    <baseTable
      ref="mainTable"
      :tableColumn="tableColumns"
      :tableData="tableData"
      :height="625"
      :showOverflow="true"
      align="right"
      headerAlign="center"
      highlightCurrentRow
      showFooter
      :footerMethod="getSummaries"
    ></baseTable>
</template>

<script>
import baseTable from "@/components/vxeTable/baseTable.vue";
export default {
  name: "ProcurementOrganizationDept",
  components: { baseTable },
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableColumns: [
        {
          type: "seq",
          label: "序号",
          width: 60,
          align: "center",
          fixed: "left",
        },
        {
          label: '部门',
          prop: 'deptName',
          width: '240',
          align: "left",
        },
        {
          label: '经费卡预算总额(元)',
          prop: 'selfPurchaseType',
          width: '200',
          formatter: ({ cellValue }) => this.formatMoney(null, null, cellValue),
          align: 'right'
        },
        {
          label: '政府集采项目',
          align: 'center',
          children: [
            {
              label: '成交项目数量',
              width: '120px',
              prop: '1',
              align: 'center'
            },
            {
              label: '成交额(元)',
              prop: '2',
              formatter: ({ cellValue }) => this.formatMoney(null, null, cellValue),
              align: 'right'
            }
          ]
        },
        {
          label: '学校集采项目',
          align: 'center',
          children: [
            {
              label: '成交项目数量',
              width: '120px',
              prop: '3',
              align: 'center'
            },
            {
              label: '成交额(元)',
              prop: '4',
              formatter: ({ cellValue }) => this.formatMoney(null, null, cellValue),
              align: 'right'
            }
          ]
        },
        {
          label: '二级单位自采项目',
          align: 'center',
          children: [
            {
              label: '成交项目数量',
              width: '120px',
              prop: '5',
              align: 'center'
            },
            {
              label: '成交额(元)',
              prop: '6',
              formatter: ({ cellValue }) => this.formatMoney(null, null, cellValue),
              align: 'right'
            }
          ]
        }
      ],
    }
  },
  methods: {
    formatMoney(row, column, cellValue) {
      return this.$formatMoney(cellValue);
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = "合计";
          return;
        }
        if (!column.property
        ) {
          sums[index] = "";
          return;
        }
        // 从 data 中计算当前列的合计
        const sum = data.reduce((acc, item) => {
          const value = parseFloat(item[column.property
          ]);
          return isNaN(value) ? acc : acc + value;
        }, 0);
        const label = column.title;
        if (label.indexOf("额") !== -1) {
          // 金额、面积格式化
          sums[index] = this.$formatMoney(sum);
        } else if(label.indexOf("数") !== -1) {
          sums[index] = sum;
        } else {
          sums[index] = "";
        }
      });
      return [sums];
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
