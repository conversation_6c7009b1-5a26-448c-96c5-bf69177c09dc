<template>
  <div>
    <b-curd ref="curdList" />
    <DownloadDialog ref="downloadDialog"></DownloadDialog>
  </div>
</template>

<script>
import DownloadDialog from './components/DownloadDialog.vue'
export default {
  name: 'projectArchivePage',
  components: { DownloadDialog },
  data() {
    return {}
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      const initParams = {
        params: {
          dataApiKey: 'archiveDataPageData',
          pageRoute: this.$getRouter()
        },
        searchForm: [
          '项目编号:PUR_PROJECT_INFO_NO_like:文本',
          '项目名称:PUR_PROJECT_INFO_NAME_like:文本'
        ],
        searchFormNum: 2,
        isShowOrderNumber: true,
        hideCurdButton: ['新增', '修改', '详情', '删除'],
        buttons: [
          {
            text: '生成归档资料',
            icon: 'el-icon-document-add',
            enabledType: '1',
            click: (row) => {
              this.generateArchive(row)
            }
          },
          {
            text: '下载归档资料',
            icon: 'el-icon-download',
            enabledType: '1',
            click: (row) => {
              this.openDownloadDialog(row)
            }
          }
        ]
      }
      this.$refs.curdList.init(initParams)
    },
    generateArchive(row) {
      const selectedRows = this.$refs.curdList.getTableCheckedRows()
      if (selectedRows.length !== 1) {
        this.$message.warning('请选择一条数据！')
        return
      }
      const projectBizid = selectedRows[0].bizid
      this.$callApiParamsConfirm(
        '确定要生成归档资料吗？',
        null,
        'startArchive',
        { ids: projectBizid },
        (result) => {
          this.init()
          return true
        }
      )
    },
    openDownloadDialog(row) {
      const selectedRows = this.$refs.curdList.getTableCheckedRows()
      if (selectedRows.length !== 1) {
        this.$message.warning('请选择一条数据！')
        return
      }
      this.$refs.downloadDialog.openDialog(selectedRows[0])
    }
  }
}
</script>

<style scoped lang="scss">

</style>
