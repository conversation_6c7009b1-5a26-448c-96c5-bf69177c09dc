<template>
  <div class="chat-history-side">
    <div class="chat-history-header">
      <h3 class="text-center w-sm">聊天历史记录</h3>
      <i class="el-icon-close" @click="close"></i>
    </div>
    <div class="search-bar mb-2">
      <el-input
        v-model="searchText"
        size="small"
        placeholder="搜索聊天记录"
        prefix-icon="el-icon-search"
        @input="handleSearchInput"
        maxlength="99"
        clearable
      />
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="全部" name="first"> </el-tab-pane>
    <el-tab-pane label="文字" name="second"></el-tab-pane>
    <el-tab-pane label="图片" name="third"></el-tab-pane>
    <el-tab-pane label="文件" name="fourth"></el-tab-pane>
   </el-tabs>
   <div class="chat-history-content" v-loading="loading">
      <el-scrollbar class="history-scrollbar" ref="historyScrollbar">
        <ul v-if="filteredMessages && filteredMessages.length > 0">
          <li v-for="(msgInfo, idx) in filteredMessages" :key="idx">
            <chat-message-item
              :mode="2"
              :mine="msgInfo.sendId == mine.id"
              :headImage="headImage(msgInfo)"
              :showName="showName(msgInfo)"
              :msgInfo="msgInfo"
              :menu="false"
            >
            </chat-message-item>
          </li>
        </ul>
        <div v-else class="no-data">
          <template v-if="searchText && messages && messages.length > 0">
            未搜索到与"{{ searchText }}"相关的内容
          </template>
          <template v-else>
            暂无数据
          </template>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>
  
  <script>
import ChatMessageItem from "./ChatMessageItem.vue";
import http from "@/views/chatPage/js/httpRequest.js";
import { mapState } from "vuex";

export default {
  name: "ChatHistorySide",
  components: {
    ChatMessageItem,
  },
  props: {
    chat: {
      type: Object,
      required: true,
    },
    friend: {
      type: Object,
    },
    group: {
      type: Object,
    },
    groupMembers: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      page: 1,
      size: 10,
      messages: [],
      loadAll: false,
      loading: false,
      activeName: 'first',
      searchText: ""
    };
  },
  computed: {
    ...mapState("chatStore", ["userInfo"]),
    mine() {
      return this.userInfo;
    },

    filteredMessages() {
      // 根据 activeName 过滤消息
      let filtered = [];
      
      if (this.activeName === 'first') {
        // 全部 - 不过滤
        filtered = this.messages;
      } else if (this.activeName === 'second') {
        // 文字 - type 为 0
        filtered = this.messages.filter(msg => msg.type === 0);
      } else if (this.activeName === 'third') {
        // 图片 - type 为 1
        filtered = this.messages.filter(msg => msg.type === 1);
      } else if (this.activeName === 'fourth') {
        // 文件 - type 为 2
        filtered = this.messages.filter(msg => msg.type === 2);
      }
      
      // 应用搜索过滤
      filtered = this.filterMessagesBySearch(filtered, this.searchText);
      
      // 对过滤后的消息进行处理
      return filtered.map(msg => {
        // 创建消息副本以避免修改原始数据
        const processedMsg = { ...msg };
        
        // 对图片和文件类型的消息进行特殊处理
        if (msg.type === 1 || msg.type === 2) {
          try {
            // 解析 content JSON 字符串
            const contentData = JSON.parse(msg.content);
            // 将解析后的数据添加到消息对象中，便于组件使用
            processedMsg.parsedContent = contentData;
          } catch (e) {
            // 如果解析失败，保持原始 content
            console.warn('Failed to parse message content:', msg.content);
          }
        }
        
        return processedMsg;
      });
    }
  },
  methods: {
    handleClick() {
        // 重置数据并重新加载
      this.messages = [];
      this.page = 1;
      this.loadAll = false;
      this.loadMessages();
    },
    close() {
      this.$emit("close");
    },
    loadMessages() {
      if (this.loadAll || !this.chat) {
        return;
      }

      this.loading = true;
      let param = {
        page: this.page++,
        size: this.size,
      };
      let isTypeRequest = false;
      // 根据 activeName 添加 type 参数
      if (this.activeName === 'second') {
        // 文字
        param.type = 0;
        isTypeRequest = true;
      } else if (this.activeName === 'third') {
        // 图片
        param.type = 1;
        isTypeRequest = true;
      } else if (this.activeName === 'fourth') {
        // 文件
        param.type = 2;
        isTypeRequest = true;
      }
      // 如果是 'first'(全部)，不添加 type 参数

      if (this.chat.type == "GROUP") {
        param.groupId = this.group.id;
      } else {
        param.friendId = this.chat.targetId;
      }
      // 构建URL
      const baseUrl = `/api/im/message/${this.chat.type.toLowerCase()}/`;
      const url = isTypeRequest ? baseUrl + 'historyType' : baseUrl + 'history';
      http({
        url,
        method: "get",
        params: param,
      })
        .then((messages) => {
          messages.forEach((m) => this.messages.push(m));
          this.loading = false;
          if (messages.length < this.size) {
            this.loadAll = true;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleSearchInput(value) {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      
      // 设置新的定时器，实现防抖
      this.searchTimer = setTimeout(() => {
        this.searchText = value;
        // 重置并重新加载数据
        this.messages = [];
        this.page = 1;
        this.loadAll = false;
        this.loadMessages();
      }, 300); // 300ms 防抖延迟
    },
    filterMessagesBySearch(messages, searchText) {
      // 如果没有搜索文本，返回所有消息
      if (!searchText) {
        return messages;
      }
      
      // 转换搜索文本为小写以进行不区分大小写的搜索
      const lowerSearchText = searchText.toLowerCase();
      
      return messages.filter(msg => {
        // 根据 activeName 和消息类型进行过滤
        switch (this.activeName) {
          case 'first': // 全部 - 搜索所有类型的消息
            if (msg.type === 0) {
              // 文字消息，直接搜索 content
              return msg.content && msg.content.toLowerCase().includes(lowerSearchText);
            } else if (msg.type === 1) {
              // 图片消息，解析 content 并搜索 originUrl
              try {
                const contentData = JSON.parse(msg.content);
                return contentData.originUrl && contentData.originUrl.toLowerCase().includes(lowerSearchText);
              } catch (e) {
                return false;
              }
            } else if (msg.type === 2) {
              // 文件消息，解析 content 并搜索 name
              try {
                const contentData = JSON.parse(msg.content);
                return contentData.name && contentData.name.toLowerCase().includes(lowerSearchText);
              } catch (e) {
                return false;
              }
            }
            return false;
            
          case 'second': // 文字
            return msg.content && msg.content.toLowerCase().includes(lowerSearchText);
            
          case 'third': // 图片
            try {
              const contentData = JSON.parse(msg.content);
              return contentData.originUrl && contentData.originUrl.toLowerCase().includes(lowerSearchText);
            } catch (e) {
              return false;
            }
            
          case 'fourth': // 文件
            try {
              const contentData = JSON.parse(msg.content);
              return contentData.name && contentData.name.toLowerCase().includes(lowerSearchText);
            } catch (e) {
              return false;
            }
            
          default:
            return false;
        }
      });
    },
    showName(msgInfo) {
      if (this.chat.type == "GROUP") {
        let member = this.groupMembers.find((m) => m.userId == msgInfo.sendId);
        return member ? member.showNickName : "";
      } else {
        return msgInfo.sendId == this.mine.id
          ? this.mine.nickName
          : this.chat.showName;
      }
    },
    headImage(msgInfo) {
      if (this.chat.type == "GROUP") {
        let member = this.groupMembers.find((m) => m.userId == msgInfo.sendId);
        return member ? member.headImage : "";
      } else {
        return msgInfo.sendId == this.mine.id
          ? this.mine.headImageThumb
          : this.chat.headImage;
      }
    },
  },
  mounted() {
    this.loadMessages();
  },
};
</script>
  
  <style lang="scss" scoped>
.chat-history-side {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 8px;
  max-height: 550px;
  .chat-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    height: 50px;
    border-bottom: 1px solid #e6e6e6;
    flex-shrink: 0;
    position: relative;
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: bold;
      width: 100%;
    }

    .el-icon-close {
      font-size: 22px;
      cursor: pointer;
      color: #909399;
      position: absolute;
      top: 16px;
      right: 5px;
      &:hover {
        color: #409eff;
      }
    }
  }

  .chat-history-content {
    flex: 1;
    overflow: hidden;

    .history-scrollbar {
      height: 100%;

      ul {
        padding: 0px;

        li {
          list-style-type: none;
          margin-bottom: 10px;
        }
      }

      .no-data {
        text-align: center;
        padding: 50px 0;
        color: #999;
        font-size: 14px;
      }
    }
  }
}
::v-deep {
  .el-tabs.el-tabs--top {
    max-height: 550px;
  }
}
</style>