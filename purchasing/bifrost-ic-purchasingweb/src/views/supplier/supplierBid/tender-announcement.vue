<template>
  <div style="height: calc(80vh - 60px);">
    <b-curd ref="curdList" />
    <supplier-bid-registration-edit ref="editDlg" @init="init" />
    <supplier-upload ref="supplierUpload" />
    <abandoning-bid-edit ref="abandoningBidEdit" />
    <login-notice-dialog ref="loginNoticeDialogRef" />
    <historyDialog ref="historyDialog"></historyDialog>
    <prepareBidding ref="prepareBidding" @init="init"></prepareBidding>
    <downFile ref="downFile"></downFile>
  </div>
</template>

<script>
import SupplierBidRegistrationEdit from "./supplier-bid-registration-edit";
// import AbandoningBid from './abandoning-bid'
import SupplierUpload from "../supplier-upload";
import AbandoningBidEdit from "./abandoning-bid-edit";
import LoginNoticeDialog from "@/views/login/login-notice-dialog";
import historyDialog from "@/views/expert/historyDialog";
import prepareBidding from "./components/prepareBidding";
import downFile from "@/views/supplier/components/downFile.vue";
export default {
  name: "tenderAnnouncement",
  components: {
    downFile,
    prepareBidding,
    historyDialog,
    SupplierBidRegistrationEdit,
    AbandoningBidEdit,
    SupplierUpload,
    LoginNoticeDialog,
  },
  data() {
    return {
      isCformPath: window.location.href.includes("/cform"),
      attList: [], // 第一次加载的附件数据
      isError: false, // 是否发生过错误
      isMonthlyPayment: false, // 是否按月付款
      needInitPlanData: true,
      initPlanIndex: 3,
      scrollerHeight: "calc((100% - 20px) / 3)",
      contractPlanHeight: "calc((100% - 20px) / 3 + 10px) !important",
      baAttachmentHeight: "calc((100% - 20px) / 3 + 10px)",
      cmMoneyType: "",
      registrationState: "",
      showEditBidFile: false,
      currentTime: ""
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      var _this = this;
      var initParams = {};
      initParams.params = {
        dataApiKey: "selectTenderAnnouncementPageData",
      };
      initParams.searchForm = [
        '状态:BID_REGISTRATION_STATUS_eq:下拉:#全部:"",待资格预审,资格预审不通过,报名成功，待上传投标文件,已上传投标文件,弃标',
        "招标编号:BIDDING_NO_like:文本",
        "采购项目名称:PROJECT_INFO_NAME_like:文本",
        "招标公告名称:NAME_like:文本",
        "公告发布时间:PUBLISH_TIME_eq:日期区间",
        "公告截止时间:END_TIME_eq:日期区间",
      ];
      initParams.callbackRowDblclick = () => {};
      initParams.searchFormNum = 4;
      initParams.buttons = [
        {
          text: "报名",
          icon: "el-icon-plus",
          enabledType: "1",
          mainButton: true,
          click: (row) => this.handleApplication(row),
        },
        {
          text: "资格预审审核记录",
          icon: "el-icon-time",
          enabledType: "1",
          click: (row) => this.btAuditHistory(row),
        },
        {
          text: "重新提交报名",
          icon: "el-icon-edit",
          enabledType: "1",
          click: (row) => this.handleReapplication(row),
        },
        {
          text: "下载采购文件",
          icon: "el-icon-download",
          enabledType: "1",
          click: (row) => this.downloadProjectFile(row),
        },
        {
          text: "上传投标文件",
          icon: "el-icon-upload2",
          enabledType: "1",
          click: (row) => this.handleDocBidUpload(row),
        },
        // { text: '上传投标函', icon: 'el-icon-upload2', enabledType: '1', click: row => (this.handlefiledUpload(row)) },
        {
          text: "弃标",
          icon: "el-icon-close",
          enabledType: "1",
          click: (row) => this.handleAbandoningBid(row),
        },
        {
          text: "查看招标公告",
          icon: "el-icon-tickets",
          enabledType: "1",
          click: (row) => this.handleAnnouncementDetails(row),
        },
        {
          text: "编制投标文件",
          icon: "el-icon-upload2",
          enabledType: "1",
          click: (row) => {
            if (row.getRowData().bidRegistrationStatus == "已上传投标文件") {
              this.$confirm(
                `您选择的项目已经生成加密后的投标文件并提交，如果您此时重新编制投标文件，系统则会删除已上传的投标文件。您是否要继续操作？`,
                "提示",
                {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                }
              ).then(() => {
                this.$message.warning(
                  "系统已将生成的加密投标文件撤回并删除，您可以在采购公共有效期内对投标文件修改后重新加密并提交。"
                );
               this.delFile(row.getRowData().projectInfoBizid)
               let timer =  setTimeout(() => {
                  this.$refs.prepareBidding.handleOpen(
                    row.getRowData()?.projectInfoBizid
                  );
                }, 1000);
                timer = null
              });
            } else {
              this.$refs.prepareBidding.handleOpen(
                row.getRowData()?.projectInfoBizid
              );
            }
          },
        },
        {
          text: "查看投标文件",
          enabledType: "1",
          dropDowns: [
            {
              text: "查看投标文件(加密)",
              icon: "el-icon-edit",
              enabledType: "1",
              click: (bt) => this.handleDown(bt, 1),
            },
            {
              text: "查看投标文件(未加密)",
              icon: "el-icon-edit",
              enabledType: "1",
              click: (bt) => this.handleDown(bt, 2),
            },
          ],
        },
      ];
      initParams.hiddenButtons = ["新增", "修改", "删除", "详情"];
      initParams.isShowOrderNumber = true;
      initParams.editDlg = this.$refs.editDlg;
      (initParams.rowCheckedCallback = async (rows) => {
        if (this.$isNotEmpty(rows)) {
          var bidRegistrationStatus = rows[0].bidRegistrationStatus;
          await _this.getInlineProduct(rows[0]?.projectInfoBizid);
          var isPrequalification = rows[0].isPrequalification; //是否需要资格预审
          if (this.$isNotEmpty(bidRegistrationStatus)) {
            // 已报名  已上传投标文件 状态 且 小于开标时间 并且策略是 是 才能编制   bidRegistrationStatus 操作编制文件
            let givenTime = new Date(rows[0].openTime).getTime();
            // 判断给定时间是否超过当前时间
            let isPast = givenTime > this.currentTime;
            if (this.showEditBidFile && isPast) {
              this.$call(
                this,
                "basePage",
                "setBtProperty",
                "上传投标文件",
                "visible",
                false
              );
              if (
                bidRegistrationStatus == "报名成功，待上传投标文件" ||
                (bidRegistrationStatus == "已上传投标文件" && isPast)
              ) {
                this.$call(
                  this,
                  "basePage",
                  "setBtProperty",
                  "编制投标文件",
                  "disabled",
                  false
                );
              } else {
                this.$call(
                  this,
                  "basePage",
                  "setBtProperty",
                  "编制投标文件",
                  "disabled",
                  true
                );
              }
            } else {
              this.$call(
                this,
                "basePage",
                "setBtProperty",
                "上传投标文件",
                "visible",
                true
              );
              this.$call(
                this,
                "basePage",
                "setBtProperty",
                "上传投标文件",
                "disabled",
                bidRegistrationStatus == "报名成功，待上传投标文件"
                  ? false
                  : true
              );
              this.$call(
                this,
                "basePage",
                "setBtProperty",
                "编制投标文件",
                "visible",
                false
              );
            }
            this.$call(
              this,
              "basePage",
              "setBtProperty",
              "报名",
              "disabled",
              true
            );
          } else {
            //是空
            this.$call(
              this,
              "basePage",
              "setBtProperty",
              "报名",
              "disabled",
              false
            );
            this.$call(
              this,
              "basePage",
              "setBtProperty",
              "编制投标文件",
              "disabled",
              true
            );
            this.$call(
              this,
              "basePage",
              "setBtProperty",
              "上传投标文件",
              "disabled",
              true
            );
          }
          if (this.$isEmpty(isPrequalification) || isPrequalification == "否") {
            this.$call(
              this,
              "basePage",
              "setBtProperty",
              "资格预审审核记录",
              "disabled",
              true
            );
            if (
              bidRegistrationStatus != "报名成功，待上传投标文件" &&
              bidRegistrationStatus != "已上传投标文件" &&
              bidRegistrationStatus != "开标现场提交电子投标文件"
            ) {
              this.$call(
                this,
                "basePage",
                "setBtProperty",
                "上传投标文件",
                "disabled",
                true
              );
              this.$call(
                this,
                "basePage",
                "setBtProperty",
                "上传投标函",
                "disabled",
                true
              );
              this.$call(
                this,
                "basePage",
                "setBtProperty",
                "弃标",
                "disabled",
                true
              );
              this.$call(
                this,
                "basePage",
                "setBtProperty",
                "下载投标文件",
                "disabled",
                true
              );
            } else {
              this.$call(
                this,
                "basePage",
                "setBtProperty",
                "下载投标文件",
                "disabled",
                false
              );
              this.$call(
                this,
                "basePage",
                "setBtProperty",
                "上传投标文件",
                "disabled",
                this.showEditBidFile ? true : false
              );
              this.$call(
                this,
                "basePage",
                "setBtProperty",
                "上传投标函",
                "disabled",
                this.showEditBidFile ? true : false
              );
              // this.$call(this, 'basePage', 'setBtProperty', '弃标', 'disabled', false)
            }
          }

          this.$call(
            this,
            "basePage",
            "setBtProperty",
            "重新提交报名",
            "disabled",
            ["资格预审不通过", "弃标"].includes(bidRegistrationStatus)
              ? false
              : true
          );
          if (isPrequalification == "是") {
            this.$call(
              this,
              "basePage",
              "setBtProperty",
              "资格预审审核记录",
              "disabled",
              false
            );
            this.$call(
              this,
              "basePage",
              "setBtProperty",
              "上传投标函",
              "disabled",
              true
            );
            // this.$call(this, 'basePage', 'setBtProperty', '弃标', 'disabled', true)
          }
          //
          this.$call(
            this,
            "basePage",
            "setBtProperty",
            "下载采购文件",
            "disabled",
            ["报名成功，待上传投标文件", "已上传投标文件", "弃标"].includes(
              bidRegistrationStatus
            )
              ? false
              : true
          );
          this.$call(
            this,
            "basePage",
            "setBtProperty",
            "弃标",
            "disabled",
            ["报名成功，待上传投标文件", "已上传投标文件"].includes(
              bidRegistrationStatus
            )
              ? false
              : true
          );
        }
      }),
        this.$refs.curdList.init(initParams);
    },
    delFile(projectId) {
        this.$callApiParams(
          "delBidFile",
          { projectId },
          () => {
            resolve();
            return true;
          },
          (error) => {
            reject(error);
          }
        );
    },
    handleDown(row, type) {
      this.$refs.downFile.handleOpen(row, type);
    },
    handlefiledUpload(row) {
      this.$refs.supplierUpload.show(row, "投标报价函");
    },
    handleDocBidUpload(row) {
      this.$refs.supplierUpload.show(row, "投标文件");
    },
    handleApplication(row) {
      this.getPunishMessage(row);
    },
    handleReapplication(row) {
      this.$refs.editDlg.show(row, "重新提交报名");
    },
    handleAbandoningBid(row) {
      this.$refs.abandoningBidEdit.show(row);
    },
    downloadProjectFile(row) {
      this.$fileDownloadBykey("downloadProjectFile", {
        id: row.getRowData().projectInfoBizid,
      });
    },
    btAuditHistory(row) {
      this.$refs.historyDialog.init(row.getRowData().registrationId);
    },

    //供应商报名
    getPunishMessage(row) {
      this.$callApiParams(
        "checkSupplierRegistration",
        {
          ids: row.getRowData().projectInfoBizid,
        },
        (result) => {
          if (result.success) {
            this.$refs.editDlg.show(row, "报名");
          }
          return true;
        }
      );
    },
    handleAnnouncementDetails(row) {
      this.$refs.loginNoticeDialogRef.show(row.getRowData().announcementId);
    },
    // 获取是否开启在线制作采购文件
    getInlineProduct(bizid = "") {
      let fetchObj = bizid ? { bizid } : {};
      return new Promise((resolve, reject) => {
        this.$callApiParams(
          "getIsEnableOnlineFurFileSysParams",
          fetchObj,
          ({ data }) => {
            const { currentTime, isEnableOnlineFurFileSysParams } = data
            this.showEditBidFile =
              isEnableOnlineFurFileSysParams !== "否" || isEnableOnlineFurFileSysParams === "是" ? true : false;
              this.currentTime = currentTime
            resolve();
            return true;
          },
          (error) => {
            reject(error);
          }
        );
      });
    },
  },
};
</script>

