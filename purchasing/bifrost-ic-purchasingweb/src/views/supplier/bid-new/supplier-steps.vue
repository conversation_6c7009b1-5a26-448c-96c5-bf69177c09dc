<template>
  <div class="bid-steps-container">
    <div class="bid-steps-tabs">
      <div
        v-for="(tabname, index) in tabs"
        :key="index"
        :class="tabclass(index)"
        @click="onclick(index)"
      >
        {{ tabname }}
      </div>
    </div>
    <div class="bid-steps-content">
      <div v-if="active == 0">
        <supplier-step1 />
      </div>
      <div v-if="active == 1">
        <supplier-step2 :bid="root.bid"/>
      </div>
      <div v-if="active == 2">
        <supplier-step3 :bid="root.bid"/>
      </div>
      <div v-if="active == 3">
        <supplier-step4 :bid="root.bid" role="供应商"/>
      </div>
    </div>
    <div>
      <div style="text-align:center;">
        <template v-if="active == 0" >
          <template v-if="bid.signinState != '已签到'">
            <el-checkbox v-model="isRead"
              label="我已阅读并遵守上述承诺" style="padding: 7px;">
            </el-checkbox>
          </template>
        </template>
      </div>
      <div class="bid-steps-button">
        <template v-if="active == 0" >
          <template v-if="bid.signinState != '已签到'">
            <el-button type="primary" @click="signin">确认</el-button>
          </template>
          <el-button v-else @click="next" type="primary">下一步</el-button>
        </template>
        <template v-else>
          <el-button type="primary" @click="pre">上一步</el-button>
          <el-button v-if="tabs.length - 1 != active" type="primary" @click="next">下一步</el-button>
          <el-button v-if="[2, 3].includes(active)" @click="clarify">澄清说明</el-button>
        </template>
        <el-button @click="back">返回项目列表</el-button>
      </div>
    </div>
    <supplier-clarify :bid="bid" ref="clarify" role="供应商"/>
  </div>
</template>

<script>
import supplierStep1 from './supplier-step1.vue'
import supplierStep2 from './supplier-step2.vue'
import supplierStep3 from './supplier-step3.vue'
// import supplierStep4 from './supplier-step4.vue'
import supplierStep4 from '@/views/expert/bid/expert-step7-single.vue'


import supplierClarify from '@/views//expert/bid/expert-clarify.vue'

export default {
  name: 'supplierSteps',
  props: { root: Object },
  components: {
    supplierStep1,
    supplierStep2,
    supplierStep3,
    supplierStep4,
    supplierClarify
  },
  data() {
    return {
      tabs: [
        "签到",
        "解密投标文件",
        "确认投标报价",
        "确认谈判结果",
      ],
      active: 0,
      step: 0,
      isRead: false,
    }
  },
  created() {
    this.$socket.addEventListener('message', (event) => {
      const data = JSON.parse(event.data)
      if(data.cmd == "RefreshBidInfo") {
        this.$callApiParams("getSupplierBiddingInfo", {
          registerId: this.bid.registerId
        }, res => {
          if(res.data) {
            Object.assign(this.bid, res.data)
          }
          return true
        })
      }
    })
  },
  mounted() {
    if(!this.bid.isNeedNegotiate == "是") {
      this.tabs.pop()
    }
    this.refreshProcess(process => this.active = process)
  },
  computed: {
    bid() {
      return this.root.bid
    }
  },
  methods: {
    refreshProcess(callback = () => {}) {
      this.$callApiParams("biddingProcess", {
        borId: this.bid.borId,
        role: "供应商"
      }, res => {
        this.step = res.data
        callback(res.data)
        return true
      })
    },
    back() {
      clearInterval(this.bid.taskId)
      this.root.visible = false
    },
    onclick(index) {
      if(this.step >= index) {
        this.active = index
      }
    },
    tabclass(index) {
      if(index == this.active) {
        return "active"
      }
      if(index <= this.step) {
        return "finish"
      }
    },
    pre() {
      this.active = Math.max(0, this.active - 1)
    },
    next() {
      this.refreshProcess(process => {
        let active = Math.min(this.tabs.length - 1, this.active + 1)
        let step = Math.max(active, this.step)
        if(step <= process) {
          this.active = active
          this.step = step
        } else if(this.active == 1) {
          this.$message.warning("主持人未公布报价!")
        }
      })
    },
    signin() {
      if(!this.isRead) {
        return this.$message.warning("请先勾选承诺框")
      }
      if(this.root.bid.docId) {
        this.$callApiParams("supplierSignIn", {
          BIZID_eq: this.root.bid.docId
        }, res => {
          this.bid.signinState = '已签到'
          this.next()
          return true
        })
      } else {
        this.next()
      }
    },
    clarify() {
      this.$refs.clarify.open()
    },
  }
}
</script>
