
<template>
  <div class="bid-steps-container">
    <div class="bid-steps-tabs">
      <div
        v-for="(tabname, index) in tabs"
        :key="index"
        :class="tabclass(index)"
        @click="onclick(index)"
        v-if="tabname != 'hidden'"
      >
        {{ tabname }}
      </div>
    </div>
    <div class="bid-steps-content">
      <div>
        <component :is="currentComponent" :bid="root.bid" :type="reviewType" role="主持人" />
      </div>
    </div>
    <div>
      <div style="text-align:center;">
        <template v-if="active == 0" >
          <template v-if="bid.bidProjectState == '待开标'">
            <el-checkbox v-model="isRead"
              label="我已阅读并遵守上述承诺" style="padding: 7px;">
            </el-checkbox>
          </template>
        </template>
      </div>
      <div class="bid-steps-button">
        <template v-if="active == 0" >
          <template v-if="bid.bidProjectState == '待开标'">
            <el-button type="primary" @click="bidOpening">确认开标</el-button>
          </template>
          <el-button v-else @click="next" type="primary">下一步</el-button>
        </template>
        <template v-else>
          <el-button type="primary" @click="pre">上一步</el-button>
          <el-button v-if="active == 3 && userId == bid.compereBizid" type="primary" @click="submitExamResult">
            提交资格性审查结果
          </el-button>
          <el-button v-if="![4,5,tabs.length - 1].includes(active)" type="primary" @click="next">下一步</el-button>
          <el-button type="primary" v-if="active == 4" @click="expertSignIn"  >
            组织专家评标
          </el-button>
          <el-button v-if="[1,2,4].includes(active)" @click="failBid">流标</el-button>
          <el-button v-if="[3,6].includes(active)" @click="clarify">澄清说明</el-button>
          <el-button v-if="active == 5" type="primary" @click="bidProcess">查看评标过程</el-button>
        </template>

        <template v-if="showSaveBidButton">
          <el-button type="primary" @click="saveBidResult">确定定标</el-button>
        </template>
        <el-button @click="back">返回项目列表</el-button>
        <host-clarify :bid="bid" ref="clarify"/>
      </div>
    </div>
  </div>
</template>

<script>
import hostStep1 from './host-step1.vue'
import hostStep2 from './host-step2.vue'
import hostStep3 from './host-step3.vue'
import hostStep4 from './host-step4.vue'
import hostStep5 from './host-step5.vue'
import hostStep6 from './host-step6.vue'
import hostStep7 from './host-step7.vue'
import hostStep8 from './host-step8.vue'
import hostStep9 from './host-step9.vue'
import hostStep10 from './host-step10.vue'
import hostStep11 from './host-step11.vue'
import hostStep12 from './host-step12.vue'
import hostStep12Vote from './host-step12-vote.vue'
import hostStep13 from '@/views/expert/bid/expert-step8.vue'
import hostStep13Price from '@/views/expert/bid/expert-step8-price.vue'
import hostStep13Tri from "./host-step13-tri.vue"

import hostClarify from '@/views/expert/bid/expert-clarify.vue'

import hostStepSingle from '@/views/expert/bid/expert-step7-single.vue'
import hostStep13Nego from '@/views/expert/bid/expert-step8-nego.vue'
import hostStep13Vote from '@/views/expert/bid/expert-step9-vote.vue'

import { downloadFile } from "@/api/file/file"

export default {
  props: { root: Object },
  name: 'hostNewHostStepsMain',
  components: {
    hostStep1, hostStep2, hostStep3, hostStep4, hostStep5, hostStep6, hostStep7,
    hostStep8, hostStep9, hostStep10, hostStep11, hostStep12, hostStep13, hostStepSingle,
    hostStep13Price, hostClarify, hostStep13Nego, hostStep13Tri, hostStep13Vote,
    hostStep12Vote
  },
  data() {
    return {
      tabs: [
        "开标",
        "供应商签到解密",
        "公布报价",
        "资格性审查",
        "资格性审查结果",
        "专家签到、推选组长",
        "开标一览表",
        "资格性审查表",
        "专家符合性审查提交情况",
        "符合性审查表",
        "价格优惠扣减",
        "综合评分法评审结果",
        "定标结果"
      ],
      active: 0,
      step: 0,
      isRead: false,
      userId: "",
    }
  },
  computed: {
    bid() {
      return this.root.bid
    },
    showSaveBidButton() {
      return this.bid.procurementMethodName == '三方比价' &&
          this.tabs[this.active] == '定标结果' &&
          this.bid.budgetAmount <= 100000
    },
    reviewType() {
      if(this.active == 7) return "资格性"
      if(this.active == 9) return "符合性"
      return ""
    },
    currentComponent() {
      let components= [
       hostStep1, hostStep2, hostStep3, hostStep4, hostStep5, hostStep6, hostStep7,
       hostStep8, hostStep9, hostStep10, hostStep11, hostStep12, hostStep13
      ]
      if(this.bid.isNeedNegotiate == '是') {
        if(this.active == 11) return hostStepSingle
        if(this.active == 12) return hostStep13Nego
      }
      if(this.active == 12) {
        if(this.bid.procurementMethodName == '三方比价') {
          return hostStep13Tri
        }
        if(this.bid.reviewMethod == '最低价格法') {
          return hostStep13Price
        }
      }
      if(this.bid.reviewMethod == '综合评分+票选') {
        if(this.active == 11) return hostStep12Vote
        if(this.active == 12) return hostStep13Vote
      }
      return components[this.active]
    },
  },
  created() {
    this.$socket.addEventListener('message', (event) => {
      const data = JSON.parse(event.data)
      if(data.cmd == "LoadFile") {
        this.loadFile()
      }
    })
  },
  mounted() {
    if(this.bid.examiner != '开标主持人') {
      this.tabs[3] = "hidden"
    }
    if(this.bid.procurementMethodName == '三方比价') {
      if(this.bid.budgetAmount <= 100000) {
        this.tabs[5] = "hidden"
      }
      this.tabs[8] = "hidden"
      this.tabs[9] = "hidden"
      this.tabs[10] = "hidden"
      this.tabs[11] = "hidden"
    }
    if(this.bid.reviewMethod == '最低价格法') {
      this.tabs[11] = "hidden"
    }
    this.$callApiParams('listBidAttachment', {
      projectId: this.bid.projectInfoBizid
    }, res => {
      this.bid.attList = res.data
      this.loadFile()
      return true
    })
    this.$callApiParams('listBidRegister', {
      projectId: this.bid.projectInfoBizid
    }, res => {
      this.bid.registerList = res.data
      return true
    })
    if(this.bid.decryptionTime) {
      this.decryptionTime = this.bid.decryptionTime
    }
    this.$callApiParams("selectBiddingSysParam", {
      key: "SUPPLIER_DECRYPTION_TIME"
    }, res => {
      let time = Number(res.data)
      if(!isNaN(time)) {
        this.bid.decryptionTime = time
      }
      return true
    })
    this.$callApiParams("selectBiddingSysParam", {
      key: "SUPPLIER_CONFIRM_TIME"
    }, res => {
      let time = Number(res.data)
      if(!isNaN(time)) {
        this.bid.confirmTime = time
      }
      return true
    })
    if(this.bid.isNeedNegotiate == '是') {
      this.$set(this.tabs, 11, procurementMethodName)
    }
    this.refreshProcess(process => {
      this.active = process
    })
    this.$getLoginUserInfo().then(userInfo => {
      this.userId = userInfo.data.userId
    })
  },
  methods: {
    loadFile() {
      this.bid.attList.forEach(att => {
        if(!att.url) {
          downloadFile(att.attId).then(res => {
              const blob = new Blob([res.data], { type: 'application/pdf' })
              att.url = URL.createObjectURL(blob)
          })
        }
      })
    },
    refreshProcess(callback = () => {}) {
      this.$callApiParams("biddingProcess", {
        borId: this.bid.borId,
        role: "主持人"
      }, res => {
        let process = res.data
        while(this.tabs[process] == "hidden" && process > 0) {
          process--
        }
        this.step = process
        callback(process)
        return true
      })
    },
    back() {
      clearInterval(this.bid.taskId)
      this.root.visible = false
    },
    onclick(index) {
      if(this.step >= index) {
        this.active = index
      }
    },
    tabclass(index) {
      if(index == this.active) {
        return "active"
      }
      if(index <= this.step) {
        return "finish"
      }
    },
    pre() {
      this.active = Math.max(0, this.active - 1)
      if(this.tabs[this.active] == 'hidden') {
        this.pre()
      }
    },
    next() {
      this.refreshProcess(process => {
        let active = Math.min(this.tabs.length - 1, this.active + 1)
        while(this.tabs[active] == "hidden" && active < this.tabs.length) {
          active++
        }
        let step = Math.max(active, this.step)
        if(step <= process) {
          this.active = active
          this.step = step
          if(this.tabs[this.active] == 'hidden') {
            this.next()
          }
        }
      })
    },
    bidOpening() {
      if(!this.isRead) {
        return this.$message.warning("请先勾选承诺框")
      }
      if(new Date(this.bid.openTime) > new Date()) {
        return this.$message.warning("还未到开标时间, 请耐心等待.")
      }
      this.$callApiParams('hostBidOpening'
      , { borId: this.bid.borId }, res => {
        this.bid.bidProjectState = "开标中"
        this.bid.bidOpeningTime = res.data.publishTime
        this.next()
      })
    },
    submitExamResult() {
      if(this.bid.examList) {
        this.$callApi("submitExamResult", this.bid.examList, res => {
          this.$message.success("提交成功")
          return true
        })
      } else {
        this.$message.warning("还有未评审的审查项")
      }
    },
    expertSignIn() {
      this.$callApiParams('updateBidStatus', {
        borId: this.bid.borId,
        status: "评标中"
      }, res => {
        this.bid.bidProjectState = "评标中"
        this.next()
        return true
      })
    },
    failBid() {
      this.$prompt('流标', '流标原因', {
        distinguishCancelAndClose: true,
        customClass: 'custom-prompt',
        inputPattern: /\S/,
        inputErrorMessage: '输入不能为空',
        showCancelButton: true,
        inputType: 'textarea',
        closeOnClickModal: false,
        inputPlaceholder: '请输入流标原因'
      }).then(({ value }) => {
        this.$callApiParams('updateBidStatus'
        , {
          borId: this.bid.borId,
          status: "流标",
          failureCause: value
        }, res => {
          this.bid.bidProjectState = "流标"
          this.bid.failureCause = value
          this.back()
          return true
        })
      })
    },
    bidProcess() {
      this.next()
    },
    clarify() {
      this.$refs.clarify.open()
    },
    saveBidResult() {
      this.$callApiParams("saveBidResult", {
        borId: this.bid.borId,
        projectId: this.bid.projectInfoBizid,
        supplierId: [this.bid.bidResult.supplierId].join(","),
        reason: this.bid.bidResult.reason
      })
    },
  }
}
</script>
