<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-16 17:45:13
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-01-27 11:12:20
-->
<template>
  <el-dialog title="历史评审记录" :visible.sync="visible" :append-to-body="true" 
      :close-on-click-modal="false" width="80%">
    <el-tabs v-model="activeName" @tab-click="handleClick" style="height: 800px;">
      <el-tab-pane label="用户管理" name="first">
        <host-history-steps :root="root" v-if="visible"/>
      </el-tab-pane>
      <el-tab-pane label="配置评审文件管理" name="second">评审文件</el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>
<script>
import hostHistorySteps from './host-history-steps.vue';

export default {
  name: 'hostHistoryTabs',
  components: { hostHistorySteps },
  data() {
    return {
      activeName: 'first',
      visible: false,
      root: {
        bid: {}
      },
    };
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
    open(borId) {
      this.$callApiParams("getHostProjectApi", { borId }, res => {
        this.root.bid = res.data
        this.visible = true
        return true
      })
    }
  }
};
</script>