<template>
  <div class="bid-review-steps-container">
    <div class="bid-steps-tabs">
      <div
        v-for="(tabname, index) in tabs"
        :class="tabclass(index)"
        @click="onclick(index)"
        :key="index"
        v-if="tabname != 'hidden'"
      >
        {{ tabname }}
      </div>
    </div>
    <div class="bid-steps-content">
      <div>
        <component :is="currentComponent" :bid="root.bid" :type="reviewType" role="主持人" />
      </div>
    </div>
    <div class="bid-steps-button">
      <el-button @click="clarify">澄清说明</el-button>
    </div>
    <host-clarify :bid="bid" ref="clarify" role="观察者"/>
  </div>
</template>

<script>
import hostStep7 from '../host-step7.vue'
import hostStep8 from '../host-step8.vue'
import hostStep9 from '../host-step9.vue'
import hostStep10 from '../host-step10.vue'
import hostStep11 from '../host-step11.vue'
import hostStep12 from '../host-step12.vue'
import hostStep13 from '@/views/expert/bid/expert-step8.vue'
import hostStep13Price from '@/views/expert/bid/expert-step8-price.vue'

import hostClarify from '@/views/expert/bid/expert-clarify.vue'

import hostStepSingle from '@/views/expert/bid/expert-step7-single.vue'
import hostStep13Nego from '@/views/expert/bid/expert-step8-nego.vue'
import hostStep13Tri from "../host-step13-tri.vue"
import hostStep12Vote from "../host-step12-vote"
import hostStep13Vote from '@/views/expert/bid/expert-step9-vote.vue'

export default {
  name: 'hostHistorySteps',
  props: { root: Object },
  components: {
    hostStep7,
    hostStep8, hostStep9, hostStep10, hostStep11, hostStep12, hostStep13, hostStepSingle,
    hostStep13Price, hostClarify, hostStep13Nego, hostStep13Tri
  },
  data() {
    return {
      tabs: [
        "开标一览表",
        "资格性审查表",
        "专家符合性审查提交情况",
        "符合性审查表",
        "价格优惠扣减",
        "综合评分法评审结果",
        "定标结果"
      ],
      active: 0,
      step: 0,
    }
  },
  computed: {
    bid() {
      return this.root.bid
    },
    showSaveBidButton() {
      return this.bid.procurementMethodName == '三方比价' &&
          this.tabs[this.active] == '定标结果' &&
          this.bid.budgetAmount <= 100000
    },
    reviewType() {
      if(this.active == 1) return "资格性"
      if(this.active == 3) return "符合性"
      return ""
    },
    currentComponent() {
      let components= [hostStep7, hostStep8, hostStep9, hostStep10, hostStep11, hostStep12, hostStep13]
      if(this.bid.isNeedNegotiate == "是") {
        if(this.active == 5) return hostStepSingle
        if(this.active == 6) return hostStep13Nego
      }
      if(this.active == 6) {
        if(this.bid.procurementMethodName == '三方比价') {
          return hostStep13Tri
        }
        if(this.bid.reviewMethod == '最低价格法') {
          return hostStep13Price
        }
      }
      if(this.bid.reviewMethod == '综合评分+票选') {
        if(this.active == 5) return hostStep12Vote
        if(this.active == 6) return hostStep13Vote
      }
      return components[this.active]
    },
  },
  mounted() {
    if(this.bid.procurementMethodName == '三方比价') {
      this.tabs[2] = "hidden"
      this.tabs[3] = "hidden"
      this.tabs[4] = "hidden"
      this.tabs[5] = "hidden"
    }
    if(this.bid.reviewMethod == '最低价格法') {
      this.tabs[5] = "hidden"
    }
    this.$callApiParams('listBidAttachment', {
      projectId: this.bid.projectInfoBizid
    }, res => {
      this.bid.attList = res.data
      return true
    })
    if(this.bid.decryptionTime) {
      this.decryptionTime = this.bid.decryptionTime
    }
    this.$callApiParams("selectBiddingSysParam", {
      key: "SUPPLIER_DECRYPTION_TIME"
    }, res => {
      let time = Number(res.data)
      if(!isNaN(time)) {
        this.bid.decryptionTime = time
      }
      return true
    })
    this.$callApiParams("selectBiddingSysParam", {
      key: "SUPPLIER_CONFIRM_TIME"
    }, res => {
      let time = Number(res.data)
      if(!isNaN(time)) {
        this.bid.confirmTime = time
      }
      return true
    })
    if(this.bid.isNeedNegotiate == "是") {
      this.$set(this.tabs, 5, this.bid.procurementMethodName)
    }
    this.refreshProcess(process => {
      while(this.tabs[process] == "hidden" && process > 0) {
        process--
      }
      this.active = process
    })
  },
  methods: {
    onclick(index) {
      if(this.step >= index) {
        this.active = index
      }
    },
    tabclass(index) {
      if(index == this.active) {
        return "active"
      }
      if(index <= this.step) {
        return "finish"
      }
    },
    refreshProcess(callback = () => {}) {
      this.$callApiParams("biddingProcess", {
        borId: this.bid.borId,
        role: "主持人"
      }, res => {
        this.step = Math.max(res.data - 6, 1)
        callback(this.step)
        return true
      })
    },
    clarify() {
      this.$refs.clarify.open()
    },
  }
}
</script>
