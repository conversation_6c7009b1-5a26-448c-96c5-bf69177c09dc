<!--
 * @Description:
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-04-19 15:10:28
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-09-11 10:41:03
-->
<template>
  <div class="fullscreen-container">
    <!--导航栏-->
    <!--功能区域-->
    <div ref="functionAreaRef" class="px-5 mb-4 mt-2 flex-shrink-0">
      <div
        class="bg-white rounded-xl shadow-lg p-4 animate-fade-in border border-gray-100"
      >
        <div
          class="flex flex-col md:flex-row justify-between items-stretch gap-4"
        >
          <!--左侧：功能按钮组 md:w-5/5-->
          <div class="w-full md:w-6/12 h-full flex flex-col">
            <h3
              class="text-base font-semibold text-gray-700 mb-1 flex items-center gap-2 justify-between line-1"
            >
              <div class="flex items-center gap-2">
                <div class="bg-indigo-100 p-1.5 rounded-lg text-indigo-600">
                  <i class="el-icon-menu"></i>
                </div>
                常用功能
              </div>
              <div class="flex">
                <button
                  class="text-sm text-primary-light hover:text-primary-dark flex items-center gap-1 px-2.5 py-1 rounded-lg hover:bg-gray-50 transition-colors"
                  @click="addFunction"
                  id="addFunctionItem"
                >
                  <i class="el-icon-plus text-base"></i>
                  <span class="text-base font-medium">添加功能</span>
                </button>
                <button
                  class="text-sm text-primary-light hover:text-primary-dark flex items-center gap-1 px-2.5 py-1 rounded-lg hover:bg-gray-50 transition-colors"
                  @click="toggleEditMode"
                  id="customizeFunctionsBtn"
                  :class="{ active: isEditMode }"
                >
                  <i
                    :class="
                      isEditMode
                        ? 'el-icon-check text-base'
                        : 'el-icon-setting text-base'
                    "
                  ></i>
                  <span class="text-base font-medium">{{
                    isEditMode ? "完成" : "编辑"
                  }}</span>
                </button>
              </div>
            </h3>
            <div
              class="functions-wrapper flex-1 bg-gradient-to-br from-gray-50 to-white p-1 rounded-xl border border-gray-100"
              :class="{ 'edit-mode': isEditMode }"
            >
              <div class="functions-grid" id="functionContainer">
                <!--自定义功能-->
                <div
                  v-for="(customFunc, index) in addedFunctions"
                  :key="index"
                  class="function-item custom-function"
                  :class="{ dragging: customFunc.isDragging }"
                  draggable="true"
                  @dragstart="dragStart(index)"
                  @dragend="dragEnd(index)"
                  @dragover.prevent
                  @drop="drop(index)"
                >
                  <button
                    class="action-btn card py-5 px-3 text-white rounded-xl shadow-md hover:shadow-lg"
                    :style="{ background: customFunc.color }"
                    :data-function="customFunc.code"
                    @click="handleFunctionClick(customFunc)"
                  >
                    <i :class="`fas ${customFunc.icon} text-3xl mb-2`"></i>
                    <span class="font-medium">{{ customFunc.text }}</span>
                  </button>
                  <button
                    class="remove-btn"
                    @click.stop="removeFunction(index, customFunc)"
                    :style="{
                      visibility:
                        isEditMode
                          ? 'visible'
                          : 'hidden',
                      opacity:
                        isEditMode  ? '1' : '0',
                    }"
                  >
                    <i class="el-icon-close text-xs"></i>
                  </button>
                  <span
                    v-show="!isEditMode && customFunc.text === '草稿箱'"
                    class="absolute -top-1 -right-1 text-xs bg-red-500 text-white px-1.5 py-0.5 rounded-full shadow-sm flex items-center justify-center min-w-[20px] h-5"
                    >{{ draftsCount || 0 }}</span
                  >
                </div>
                <!--添加功能按钮-->
                <div class="function-item" id="allFunctionItem">
                  <div
                    class="add-function-btn rounded-xl shadow-sm hover:shadow-md"
                    @click="showAllFunctions"
                  >
                    <i class="el-icon-more"></i>
                    <span>全部功能</span>
                  </div>
                </div>
              </div>
            </div>
            <h3
              class="text-base font-semibold text-gray-700 mt-1.5 mb-1 flex items-center gap-2 justify-between line-1"
            >
              <div class="flex items-center gap-2">
                <div class="bg-indigo-100 p-1.5 rounded-lg text-indigo-600">
                  <i class="el-icon-s-claim"></i>
                </div>
                我的项目统计
              </div>
            </h3>
            <div class="el-card__body p-0">
              <div class="grid grid-cols-3 gap-4">
                <div
                  v-for="(stat, index) in projectStats"
                  :key="index"
                  class="stat-card"
                >
                  <div class="h-full flex justify-around items-center">
                    <div class="stat-label">{{ stat.label }}</div>
                    <div class="stat-value">{{ stat.value }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--右侧：公共待办区域 md:w-2/5-->
          <div
            class="w-full md:w-6/12 p-4 bg-gradient-to-br from-gray-50 to-white rounded-xl border border-gray-100 flex flex-col shadow-sm"
            style="max-height: 333px;"
          >
            <div
              class="text-base font-semibold text-gray-700 mb-1 flex items-center gap-2 flex-start line-1 font-semibold text-gray-700 mb-1 flex items-center gap-2 relative"
            >
              <div class="bg-blue-100 p-1.5 rounded-lg text-blue-600">
                <i class="el-icon-s-fold"></i>
              </div>
              <span class="relative">
                <span> 我的待办</span>
                <span
                  class="absolute -top-2 -right-6 text-xs bg-red-500 text-white px-1.5 py-0.5 rounded-full shadow-sm flex items-center justify-center min-w-[20px] h-5"
                  >{{ todos.length }}</span
                >
              </span>
            </div>
            <div class="pr-1" style="flex:1; overflow-y: auto;">
              <div
                v-for="(todo, index) in todos"
                :key="index"
                class="todo-item flex items-center hover:shadow-md cursor-pointer"
                @click="handleTodoAction(todo)"
              >
                <div class="flex items-center flex-1">
                  <!-- <img :src="todo.icon" class="w-5 h-5"> -->
                  <div class="w-5 h-5 rounded-full bg-blue-300  text-center" style="color: #fff;line-height: 1.20rem;background-color: rgb(0 157 255);">{{ index+1}}</div>

                  <div class="text-gray-700 leading-8 flex-1 ml-3">{{ todo.text }}</div>
                </div>
                <button
                  class="text-blue-500 hover:text-blue-700 btn-transparent hover:bg-blue-50 p-1.5 rounded-lg shrink-0 transition-all"
                >
                  <i class="el-icon-right" style="font-size: 22px"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--项目表格-全屏显示-->
    <div class="project-table-container">
      <div
        class="bg-white overflow-hidden rounded-xl h-full border border-gray-100 flex flex-col justify-between"
      >
        <div class="flex flex-col">
          <div
            class="flex justify-between items-center p-3 pb-1 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white"
          >
            <h3
              class="text-base font-semibold text-gray-700 flex items-center gap-2 justify-between line-1"
            >
              <div class="bg-indigo-100 p-1.5 rounded-lg text-indigo-600">
                <i class="el-icon-s-custom"></i>
              </div>
              我的项目
            </h3>
            <div class="flex items-center">
              <div class="relative flex items-center max-w-2xl">
                <el-input
                  placeholder="请输入项目名称或项目编号"
                  prefix-icon="el-icon-search"
                  class="text-lg absolute"
                  clearable
                  v-model="searchQuery"
                  style="width: 218px"
                >
                </el-input>
              </div>
              <button
                class="ml-2 px-4 py-1.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 focus:outline-none flex items-center gap-1.5 transition-all shadow-md hover:shadow-lg"
                @click="searchProjects"
                style="border: none !important"
              >
                <i class="el-icon-search"></i>
                查询
              </button>
            </div>
          </div>
          <div class="overflow-x-auto table-container-fixed">
            <el-table
              ref="elTableRef"
              :data="projects"
              style="width: 100%"
              :height="tableHeight"
            >
              <el-table-column
                prop="index"
                label="序号"
                width="70"
                align="center"
              >
                <template #default="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column
                prop="projectInfoName"
                label="项目名称"
                width="270"
              >
                <template #default="scope">
                  <div class="flex items-center">
                    <div>
                      <div class="font-medium">
                        {{ scope.row.projectInfoName }}
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="projectInfoNo"
                label="项目编号"
                width="320"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  {{ row.projectInfoNo }}
                </template>
              </el-table-column>
              <el-table-column>
                <template #header>
                  <div style="display: inline-flex; align-items: center">
                    <span>项目进度</span>
                    <el-tooltip
                      effect="dark"
                      content="点击进度中的节点可以展开当前项目可以进行操作的功能按钮，没有展开时说明当前项目没有可以操作的功能按钮"
                      placement="top"
                    >
                      <i
                        class="el-icon-question"
                        style="
                          margin-left: 5px;
                          cursor: pointer;
                          font-size: 14px;
                        "
                      ></i>
                    </el-tooltip>
                    <span class="text-gray-400 text-xs font-normal ml-2">(绿色表示项目已完成节点，蓝色表示进行中节点，灰色表示尚未进行的节点)</span>
                  </div>
                </template>
                <template #default="{ row }">
                  <div class="flex flex-wrap" style="gap: 8px;">
                    <div
                      v-for="(status, statusIndex) in row.statuses"
                      :key="statusIndex"
                      :class="`status-tag ${status.class}`"
                      @click="toggleExpand(row, statusIndex, status)"
                      class="relative"
                    >
                      <i :class="status.icon" class="mr-1"></i>
                      <span class="relative" style="line-height: 22px;">{{ status.text }}</span>
                      <span
                      v-if="status.hasChange"
                      class="absolute -top-2.5 -right-2 text-xs bg-red-500 text-white px-1.5 py-0.35 rounded-full shadow-sm flex items-center justify-center min-w-[20px]"
                      @click.prevent.stop="handleClick(status)"
                      >{{status.changeCount}}</span
                    >
                    <span
                      v-if="status.hasQuestion"
                      class="absolute -top-2.5 -right-2 text-xs bg-red-500 text-white px-1.5 py-0.35 rounded-full shadow-sm flex items-center justify-center min-w-[20px]"
                      @click.prevent.stop="handleClick(status)"
                      >{{status.changeCount}}</span
                    >
                    </div>

                  </div>
                  <!-- 展开内容 -->
                  <div
                    v-if="row.buttons.length > 0"
                    :id="`expand-${row.projectInfoNo}`"
                    :class="`expanded-row ${row.isExpanded ? 'active' : ''}`"
                  >
                    <div class="flex gap-2">
                      <el-button
                        v-for="(btn, btnIndex) in row.buttons"
                        :key="btnIndex"
                        type="primary"
                        size="small"
                        @click="nodeBtnClick(btn)"
                      >
                        <i :class="btn.icon"></i>
                        {{ btn.text }}
                        <el-tooltip
                          v-if="btn.remark"
                          effect="dark"
                          :content="`${btn.text}功能说明：${btn.remark}`"
                          placement="top"
                        >
                          <span
                            class="inline-flex items-center justify-center w-4 h-4 ml-2 text-xs rounded-full bg-white bg-opacity-30 text-white hover:bg-opacity-40 transition-all"
                            >?</span
                          >
                        </el-tooltip>
                      </el-button>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <!--添加功能选择器弹窗-->
    <div
      :class="`function-selector ${showFunctionSelector ? 'show' : ''}`"
      id="functionSelector"
    >
      <div class="function-selector-content p-6">
        <div class="flex justify-between items-center mb-5">
          <h3
            class="text-base font-semibold text-gray-700 mt-1.5 mb-1 flex items-center gap-2 justify-between line-1"
          >
            <div class="bg-indigo-100 p-1.5 rounded-lg text-indigo-600">
              <i class="el-icon-circle-plus-outline"></i>
            </div>
            添加常用功能
          </h3>
          <button
            id="closeSelectorBtn"
            class="btn-transparent text-gray-500 hover:text-gray-700 focus:outline-none hover:bg-gray-100 p-2 rounded-full transition-colors"
            @click="closeFunctionSelector"
          >
            <i class="el-icon-close text-2xl"></i>
          </button>
        </div>
        <p class="text-gray-600 mb-5">选择您想要添加的功能：</p>
        <div id="functionOptions" class="grid grid-cols-3 gap-4 mb-6">
          <button
            v-for="(option, index) in functionOptions"
            :key="index"
            class="card p-4 flex flex-col items-center"
            :style="{
              backgroundColor: `${option.color}`,
              color: `#fff`,
            }"
            :data-function="option.code"
            :data-icon="option.icon"
            :data-color="option.colorClass"
            :disabled="isOptionDisabled(option.bizid)"
            @click="toggleOptionSelection(index)"
            :class="{
              selected: option.isSelected,
              'ring-2': option.isSelected,
              'ring-blue-500': option.isSelected,
              'opacity-50': isOptionDisabled(option.bizid),
              'cursor-not-allowed': isOptionDisabled(option.bizid),
            }"
          >
            <i :class="`fas ${option.icon} text-2xl mb-2`"></i>
            <span>{{ option.text }}</span>
          </button>
        </div>
        <div class="flex justify-end pt-3 border-t border-gray-200">
          <button
            id="confirmAddBtn"
            class="border-transparent bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-5 py-2.5 rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all shadow-md hover:shadow-lg flex items-center gap-2"
            :disabled="selectedOptions.length === 0"
            @click="confirmAddFunctions"
          >
            <i class="el-icon-check"></i>
            <span
              >确认添加{{
                selectedOptions.length > 0 ? `(${selectedOptions.length})` : ""
              }}</span
            >
          </button>
        </div>
      </div>
    </div>
    <!--全部功能弹窗-->
    <div
      :class="`all-btn-selector ${showAllBtnSelector ? 'show' : ''}`"
      id="allBtnSelector"
    >
      <div class="function-selector-content p-6">
        <div class="flex justify-between items-center mb-5">
          <h3
            class="text-base font-semibold text-gray-700 mt-1.5 mb-1 flex items-center gap-2 justify-between line-1"
          >
            <div class="bg-indigo-100 p-1.5 rounded-lg text-indigo-600">
              <i class="el-icon-more"></i>
            </div>
            全部功能
          </h3>
          <button
            id="closeAllSelectorBtn"
            class="btn-transparent text-gray-500 hover:text-gray-700 focus:outline-none hover:bg-gray-100 p-2 rounded-full transition-colors"
            @click="closeAllBtnSelector"
          >
            <i class="el-icon-close text-2xl"></i>
          </button>
        </div>
        <div id="functionOptions" class="grid grid-cols-4 gap-4 mb-6">
          <button
            v-for="(option, index) in allFunctionOptions"
            :key="index"
            class="card py-4 px-0.5 flex flex-col items-center"
            :data-function="option.code"
            :data-icon="option.icon"
            :data-color="option.color"
            :style="{
              backgroundColor: `${option.color}`,
              color: `#fff`,
            }"
            :class="`${option.color}`"
            @click="handleFunctionClick(option)"
          >
            <i :class="`fas ${option.icon} text-2xl mb-2`"></i>
            <span>{{ option.text }}</span>
          </button>
        </div>
        <div class="flex justify-end pt-3 border-t border-gray-200">
          <button
            id="closeBtn"
            class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-5 py-2.5 rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all shadow-md hover:shadow-lg flex items-center gap-2"
            @click="closeAllBtnSelector"
            style="border: none !important"
          >
            <i class="el-icon-close"></i>
            <span>关闭</span>
          </button>
        </div>
      </div>
    </div>
    <div class="flex justify-between items-center px-5">
      <span class="pagination-info">
        共 {{ dataTotal }} 条记录 第{{ current }} /
        {{ Math.ceil(dataTotal / size) ? Math.ceil(dataTotal / size) : 1 }}
        页
      </span>
      <el-pagination
        class="listContentPagination"
        background
        layout="prev, pager, next, sizes, jumper"
        :page-sizes="pageSizes"
        :page-size="size"
        :total="dataTotal"
        :current-page="current"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <needPurchaseDialog ref="needPurchaseDialog"  @refresh="init()"/>
    <isPurchPage ref="isPurchPage"  @refresh="init()" />
    <draftsDialog ref="draftsDialog" @refresh="init()" />
    <isHomePageBuilding ref="isHomePageBuilding" />
    <modalManager ref="modalManager" />
    <changeTypeDialog ref="changeTypeDialog" />
  </div>
</template>

  <script>
import draftsDialog from './components/draftsDialog.vue';
import isHomePageBuilding from './isBuilding/index.vue'
import { modalOrPageConfig, nodeConfig } from './isPurchComponts/components/modalOrPageConfig'
import changeTypeDialog from './components/changeTypeDialog.vue'
// 定义颜色常量
const COLOR_CONSTANTS = [
  { color: "#f59e0b", darkColor: "#f59e0b", colorClass: "orange-600" },
  { color: "#ef4444", darkColor: "#dc2626", colorClass: "red-600" },
  { color: "#eab308", darkColor: "#ca8a04", colorClass: "yellow-600" },
  { color: "#3b82f6", darkColor: "#2563eb", colorClass: "blue-600" },
  { color: "#6b7280", darkColor: "#4b5563", colorClass: "gray-600" },
  { color: "#14b8a6", darkColor: "#0d9488", colorClass: "teal-600" },
  { color: "#ec4899", darkColor: "#db2777", colorClass: "pink-600" },
  { color: "#8b5cf6", darkColor: "#7c3aed", colorClass: "indigo-600" },
  { color: "#22c55e", darkColor: "#16a34a", colorClass: "green-600" },
];
export default {
  name: "homePageHandle",
  components: { draftsDialog, isHomePageBuilding, changeTypeDialog },
  data() {
    return {
      current: 1,
      pageSizes: [5, 10],
      size: 5,
      dataTotal: 0,
      isEditMode: false,
      addedFunctions: [],
      todos: [],
      projects: [],
      projectStats: [
        { label: "总项目数", value: 0, iconClass: "el-icon-s-fold" },
        { label: "采购中项目数", value: 0, iconClass: "el-icon-s-unfold" },
        { label: "已完成项目数", value: 0, iconClass: "el-icon-s-operation" },
      ],
      searchQuery: "",
      pageSize: 5,
      currentPage: 1,
      showFunctionSelector: false,
      showAllBtnSelector: false,
      showAllBtnSelectorKey: 1,
      functionOptions: [],
      selectedOptions: [],
      allFunctionOptions: [], //
      tableHeight: 0,
      debounceHeight: null, // 动态设置高度防抖
      observer: null, // 观察器
      draftsCount: 0, // 草稿箱数量
    };
  },
  created() {},
  computed: {},
  methods: {
    addFunction() {
      this.showFunctionSelector = true;
      this.functionOptions.forEach((option) => {
        option.isSelected = false;
      });
      this.selectedOptions = [];
    },
    toggleEditMode() {
      this.isEditMode = !this.isEditMode;
    },
    removeFunction(index, item) {
      if (item.isDisplay === "是") {
        this.$message.warning("当前常用功能默认必须已显示，不能删除！");
        return;
      }
      this.$callApiParamsConfirm(
        "您确定需要删除所选择的功能吗？",
        null,
        "currentUserDeleteCommon",
        {
          ids: item.bizid,
        },
        (result) => {
          this.init();
          this.$message.success("删除成功");
          this.isEditMode = false;
          return true;
        },
        null
      );
      this.addedFunctions.splice(index, 1);
    },
    showAllFunctions() {
      this.$nextTick(() => {
        this.showAllBtnSelector = true;
      });
    },
    closeFunctionSelector() {
      this.showFunctionSelector = false;
    },
    closeAllBtnSelector() {
      this.showAllBtnSelector = false;
    },
    toggleOptionSelection(index) {
      if (!this.isOptionDisabled(this.functionOptions[index].bizid)) {
        this.functionOptions[index].isSelected =
          !this.functionOptions[index].isSelected;
        if (this.functionOptions[index].isSelected) {
          this.selectedOptions.push(this.functionOptions[index]);
        } else {
          const indexToRemove = this.selectedOptions.findIndex(
            (option) => option.code === this.functionOptions[index].bizid
          );
          if (indexToRemove > -1) {
            this.selectedOptions.splice(indexToRemove, 1);
          }
        }
      }
    },
    isOptionDisabled(bizid) {
      return (
        this.allFunctionOptions.some((func) => func.bizid === bizid) ||
        this.addedFunctions.some((func) => func.bizid === bizid)
      );
    },
    // 确定添加
    confirmAddFunctions() {
      // 关闭选择器 传递参数给后端
      this.$callApi(
        "currentUserAddCommon",
        { ids: this.selectedOptions.map((option) => option.bizid) },
        (res) => {
          this.getlistCurrentUserCommon(); // 查询当前岗位下的全部功能
          this.getlistCurrentPostCommon(); // 查询当前用户的常用功能
        }
      );
      this.showFunctionSelector = false;
    },
    toggleExpand(row, index, todo) {
      console.log("toggleExpand", row, index, todo);
      // 隐藏所有行的展开内容
      this.projects.forEach((project) => {
        if (project !== row) {
          project.isExpanded = false;
        }
      });
      // 切换当前点击行的展开状态
      row.isExpanded = !row.isExpanded;
      // 展开的时候需要显示滚动条
      this.setTableHeight(row.isExpanded ? 185 : 180)
      if (todo?.class === 'active-tag') {
        this.handleNodeAction(todo)
      }
      // this.$refs.modalManager.openModal(modalOrPageConfig[todo.state], row);
    },
    searchProjects() {
      // 实现搜索逻辑
      this.getProjects();
    },
    handleNodeAction(node) {
      if (nodeConfig[node.stateName]?.type === 'modal') {
        this.$refs.modalManager.openModal(nodeConfig[node.stateName].content, node);
      } else if (nodeConfig[node.stateName]?.type === 'page') {
        this.$toPath(nodeConfig[node.stateName].content);
      }
    },
    handleTodoAction(todo) {
      console.log('handleTodoAction', todo)
      // 处理待办事项操作
      // 2025813修改 跳转到我要审核第四页面
      // this.$toPath(`/${todo.url}`);
      if (modalOrPageConfig[todo.stateName]?.type === 'page') {
        this.$toPath(modalOrPageConfig[todo.stateName].content);
      } else if (modalOrPageConfig[todo.stateName]?.type === 'modal') {
        this.$refs.modalManager.openModal(modalOrPageConfig[todo.stateName].content, todo);
      }
    },
    dragStart(index) {
      if (this.isEditMode) {
        this.addedFunctions[index].isDragging = true;
      }
    },
    dragEnd(index) {
      this.addedFunctions[index].isDragging = false;
    },
    drop(index) {
      if (this.isEditMode) {
        const draggedIndex = this.addedFunctions.findIndex(
          (func) => func.isDragging
        );
        if (draggedIndex > -1) {
          const draggedItem = this.addedFunctions[draggedIndex];
          this.addedFunctions.splice(draggedIndex, 1);
          this.addedFunctions.splice(index, 0, draggedItem);
        }
      }
    },
    init() {
      // 初始化逻辑
      this.getlistCurrentUserCommon(); // 查询当前用户下的全部功能
      this.queryStatistic(); //统计项目数量
      this.getlistCurrentPostCommon(); // 查询当前岗位下的全部功能
      this.getQueryCurrentTodo(); // 我的待办
      this.getProjects(); // 我的项目
      this.getDraftsCount(); // 我的草稿箱数量
    },

    getlistCurrentUserCommon() {
      this.$callApi("listCurrentUserCommon", {}, (res) => {
        const resp = res.data.map((item) => {
          // 哈希函数，将 bizid 转换为一个索引值
          const hash = (str) => {
            let hashValue = 0;
            for (let i = 0; i < str.length; i++) {
              hashValue = (hashValue << 5) - hashValue + str.charCodeAt(i);
              hashValue = hashValue & hashValue; // Convert to 32bit integer
            }
            return Math.abs(hashValue);
          };
          // 根据 bizid 计算索引
          const index = hash(item.bizid) % COLOR_CONSTANTS.length;
          const fixedColorObj = COLOR_CONSTANTS[index];
          return {
            bizid: item.bizid,
            icon: item.icon,
            text: item.commonName,
            // color: `${fixedColorObj.darkColor}`,
            color: item.backgroundColor || `#0094FE`,
            colorClass: fixedColorObj.colorClass,
            isDragging: false,
            isDisplay: item.isDisplay,
            openWith: item.openWith,
            routeUrl: item.routeUrl,
          };
        });
        this.addedFunctions = resp;
        return true;
      },() => {},{ isSave:false });
    },
    // 点击全部功能 / 添加功能显示 列表
    getlistCurrentPostCommon() {
      this.$callApi("listCurrentPostCommon", {}, (res) => {
        const resp = res.data.map((item) => {
          // 哈希函数，将 bizid 转换为一个索引值
          const hash = (str) => {
            let hashValue = 0;
            for (let i = 0; i < str.length; i++) {
              hashValue = (hashValue << 5) - hashValue + str.charCodeAt(i);
              hashValue = hashValue & hashValue; // Convert to 32bit integer
            }
            return Math.abs(hashValue);
          };
          // 根据 bizid 计算索引
          const index = hash(item.bizid) % COLOR_CONSTANTS.length;
          const fixedColorObj = COLOR_CONSTANTS[index];
          return {
            bizid: item.bizid,
            icon: item.icon,
            text: item.commonName,
            // color: `${fixedColorObj.darkColor}`,
            color: item.backgroundColor || `#0094FE`,
            colorClass: fixedColorObj.colorClass,
            isDragging: false,
            isDisplay: item.isDisplay,
            openWith: item.openWith,
            routeUrl: item.routeUrl,
          };
        });
        this.functionOptions = resp;
        this.allFunctionOptions = resp;
        return true;
      },() => {},{ isSave:false });
    },
    // 我的项目数量统计
    queryStatistic() {
      this.$callApi("queryProjectStatistics", {}, (res) => {
        if (res.data) {
          if (Array.isArray(res.data)) {
            // 遍历 projectStats 数组
            this.projectStats.forEach((stat) => {
              // 在接口返回的数据中查找匹配项
              const apiItem = res.data.find((item) => item.name === stat.label);
              if (apiItem) {
                stat.value = apiItem.value;
              }
            });
          }
        }
        return true;
      },() => {},{ isSave:false });
    },
    // 我的待办
    getQueryCurrentTodo() {
      const levelHigh = require('@/assets/svg/dynamic-flame-high.svg')
      const levelMedium = require('@/assets/svg/dynamic-flame-medium.svg')
      const levelLow = require('@/assets/svg/dynamic-flame-low.svg')
      const levels = [
        { level: 1, icon: levelHigh },
        { level: 2, icon: levelMedium },
        { level: 3, icon: levelLow },
      ];

      // 1-3  紧急层度 给对应的颜色标识
      // 1-3  紧急层度 给对应的颜色标识
      this.$callApi("queryCurrentTodo", {}, ({ data }) => {
        // 检查 res.data 是否存在且不为空，以及 todoTaskVoList 是否为数组
        if (data && Array.isArray(data.todoTaskVoList)) {
          const todos = data.todoTaskVoList.map((item) => {
            return {
              ...item,
              color:
                item.level === 1
                  ? "bg-red-500"
                  : item.level === 2
                  ? "bg-yellow-500"
                  : item.level === 3
                  ? "bg-blue-500"
                  : "",
              text: item.name,
              icon: levels.find((level) => level.level === item.level)?.icon || levelLow,
            };
          });
          this.$set(this, 'todos', todos)
        } else {
          // 如果数据不符合要求，将 todos 置为空数组
          this.todos = [];
        }
        return true;
      },() => {},{ isSave:false });
    },
    getDraftsCount() {
      this.$callApi('getDraftCount', {}, ({ data }) => {
        this.draftsCount = data || 0;
        return true;
      },() => {},{ isSave:false });
    },
    // 我的项目
    getProjects() {
      const params = {
        current: this.current,
        size: this.size,
        key: this.searchQuery,
      };
      this.$callApiParams(
        "queryPurchasingAgentProcessNode",
        { ...params },
        ({ data }) => {
          const respData = data.data || [];
          if (respData && Array.isArray(respData)) {
            // 转换后端数据到页面所需的数据结构
            this.projects = respData.map((item) => {
              return {
                id: item.id,
                projectInfoName: item.projectInfoName,
                state: item.state || "未知类型",
                projectInfoNo: item.projectInfoNo,
                // todo  增加节点显示内容  有变更/有质疑
                statuses: item.nodes.map((node) => {
                  let className = "";
                  switch (node.currentNodeStatus) {
                    case "进行中":
                      className = "active-tag";
                      break;
                    case "未开始":
                      className = "pending-tag";
                      break;
                    case "结束":
                      className = "done-tag";
                      break;
                    // 可根据实际情况添加更多状态
                    default:
                      className = "pending-tag";
                  }
                  return {
                    class: className,
                    icon:
                      className === "active-tag"
                        ? "el-icon-video-play"
                        : className === "pending-tag"
                        ? "el-icon-time"
                        : "el-icon-success",
                    text: node.moduleNodeName,
                    bizid: node.projectInfoBizid,
                    stateName: node.stateName,
                    projectType: node.projectType,
                    purProjectInfoBizid: node.projectInfoBizid,
                    hasChange: node?.tag  && node?.tag === '有变更' ? true : false,
                    hasQuestion: node?.tag  && node?.tag === '有质疑'  ? true : false,
                    changeCount: node?.tag || '',
                    recordVos: node?.recordVos || [],
                  };
                }),
                isExpanded: false,
                buttons: item.nodeButtonVoList.map((btn) => ({
                  text: btn.buttonName,
                  icon: btn.icon,
                  nodeName: btn.nodeName,
                  routeUrl: btn.routeUrl,
                  postBizid: btn.postBizid,
                  remark: btn.remark,
                })),
              };
            });
            this.dataTotal = data.count || data.length || 0;
          }
          return true;
        },() => {},{ isSave:false }
      );
    },
    handleCurrentChange(cpage) {
      this.current = cpage;
      this.getProjects();
    },
    handleSizeChange(psize) {
      this.size = psize;
      // 当每页数量改变时，将当前页码重置为第一页
      this.current = 1;
      // 调用获取项目数据的方法，传入当前页码和每页数量
      this.getProjects();
    },
    nodeBtnClick(btn) {
      // 节点按钮点击事件处理逻辑
      const { routeUrl, postBizid } = btn;
      if (routeUrl) {
        this.$toPath(`/${routeUrl}`);
      }
    },
    handleFunctionClick(btn) {
      const { openWith, routeUrl, bizid } = btn;
      if (btn.text === "草稿箱") {
        this.$refs.draftsDialog.showDialog();
        return
      }
      if (openWith === "页面") {
        this.$toPath(`/${routeUrl}`, { bizid });
      }
      if(btn.text === '设备部推送的项目') {
        this.$refs.isHomePageBuilding.openDialog()
        return
      }
      if(btn.text === '我要采购') {
        // this.$refs.needPurchaseDialog.showDialog()
        this.$refs.isPurchPage.showDialog()
        return
      }
      if (openWith === "模块") {
        this.$toPath(`/${routeUrl}`, { bizid });
      }
    },
    handleClick(row){
      this.$refs.changeTypeDialog.openDialog(row)
    },
    setTableHeight(otherHeight = 180) {
      const pageH = document.documentElement.clientHeight || document.body.clientHeight
      // 功能区域的高度
      const topH = this.$refs.functionAreaRef.offsetHeight || 0
      // 获取分页器的高度 + 外边距
      const paginationH = document.querySelector('.el-pagination')?.getBoundingClientRect().height + 16
      this.tableHeight = pageH - topH - paginationH - otherHeight +20;
    }
  },
  async mounted() {
    // 加载用户配置
    this.init();
    // 动态设置表格高度
    this.observer = new MutationObserver(() => {
      this.$nextTick(() => {
        this.setTableHeight();
      });
    });
    this.observer.observe(this.$refs.functionAreaRef, {
      childList: true,
      subtree: true,
      characterData: true,
    })
    this.debounceHeight = this.$debounce(() => this.setTableHeight(), 300);
    window.addEventListener("resize", this.debounceHeight);

    // 将事件处理函数保存为组件实例的属性，以便后续移除
    this.socketMessageHandler = (event) => {
      console.log("收到待办刷新消息-------------------------------------》》》》》》》》》", event);
      const data = JSON.parse(event.data)
      if(data.cmd == "RefreshTodo") {
        this.getQueryCurrentTodo();
      }
    };
    this.$socket.addEventListener('message', this.socketMessageHandler);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.debounceHeight);
    this.observer && this.observer.disconnect();

    // 移除 socket 事件监听器
    if (this.socketMessageHandler) {
      this.$socket.removeEventListener('message', this.socketMessageHandler);
    }
  },
  activated() {
    this.init()
  }
};
</script>

<style scoped lang="scss">
@import "../homePageHandle/homePageHandle.scss";
@import "../../assets/tailwindcss.css";
@import "../../assets/preflight.css";
@import "../homePageHandle/index.css";
::v-deep {
  .el-card__body {
    padding: 0!important;
  }
   .el-table::before{
    display: none!important;
   }
}
</style>
