<!--
 * @Description: 3 我要采购
 * @Version: 2.0
 * @Autor: 作者姓名
 * @Date: 创建日期
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-09-15 17:40:50
-->
<template>
  <div>
    <el-dialog
      :title="homeTitle"
      :visible.sync="dialogVisible"
      width="80%"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      v-if="dialogVisible"
    >
    <template #title>
        <div class="text-left text-base text-[#000] font-semibold">
          <span>{{ homeTitle }}</span>
        </div>
      </template>
      <customSteps 
          :active="2"
         />
      <div class="mb-2 mt-2 font-bold" style="color: #006CFF;">
        请填写或选择本项目的采购信息
      </div>
      <div class="content" style="overflow: overlay; height: 70vh">
      <div>
        <MultiLevelSelector
          ref="multiLevelSelector"
          :formData="needPurchaseDialogObj"
        />
      </div>
      </div>
      <div slot="footer" class="dialog-footer text-center">
        <el-button @click="handlePrve">上一步</el-button>
        <el-button type="primary" @click="handleSure" :loading="sureLoading">保存</el-button>
        <el-button @click="handleNext">下一步</el-button>
      </div>
    </el-dialog>
    <fillProcurementDialog
      ref="fillProcurementDialog"
      @handlePrveFillProcurementDialog="handlePrveFillProcurementDialog"
      :nextObj.sync="nextObj"
      @closeDialog="dialogVisible = false"
    />
    <chooseEmallDialog
      ref="chooseEmallDialog"
      @handlePrveFillProcurementDialog="handlePrveFillProcurementDialog"
      :nextObj="nextObj"
      @closeDialog="dialogVisible = false"
    />
    <chooseSchoolDialog
      ref="chooseSchoolDialog"
      @handlePrveFillProcurementDialog="handlePrveFillProcurementDialog"
      :nextObj="nextObj"
      @closeDialog="dialogVisible = false"
    />
    <!-- 采购记录表 -->
    <chooseRecordDialog
      ref="chooseRecordDialog"
      @handlePrveFillProcurementDialog="handlePrveFillProcurementDialog"
      :nextObj="nextObj"
      @closeDialog="dialogVisible = false"
    />
  </div>
</template>
<script>
import fillProcurementDialog from "./fillProcurementDialog";
import MultiLevelSelector from "./multiLevelSelector"
export default {
  name: "needPurchaseDialog",
  components: { fillProcurementDialog, MultiLevelSelector },
  props: {},
  data() {
    return {
      dialogVisible: false, // 控制对话框的显示与隐藏
      homeTitle: "我要采购-填写、选择或确认采购信息", // 对话框标题
      needPurchaseDialogObj: {
        purProjectInfoName: "",
        amount: "",
        procurementMethodName: "",
        procurementMethodBizid: "",
        purchaseClassification: [],
        purchaseActuator: "",
        procurementCategory: "",
        selfPurchaseDescription: "",
        sbflag: false, // 设备类
        fwflag: false, // 服务类
        gcflag: false, // 工程类
        hwflag: false, // 货物类
        dzmcflag: false, // 电子卖场
      },
      // 新增：下拉框选项数据（集中管理）
      settingProcurementMethodEntityList: [],
      targetTypeOptions: [],
      purchaseActuatorEntityList: [],
      organizationFormOptions: [],
      documentProjectOptions: [],
      rules: {
        purProjectInfoName: [
          { required: true, message: "请输入采购项目名称", trigger: "blur" },
        ],
        procurementMethodName: [
          { required: true, message: "请选择采购方式", trigger: "blur" },
        ],
        purchaseClassification: [
          { required: true, message: "请选择采购标的类别", trigger: "blur" },
        ],
        purchaseActuator: [
          { required: true, message: "请选择采购执行机构", trigger: "blur" },
        ],
        procurementCategory: [
          { required: true, message: "请选择采购组织形式", trigger: "blur" },
        ],
        selfPurchaseDescription: [
          {
            required: true,
            message: "请选择文献资料类项目",
            trigger: "blur",
          },
        ],
      },
      typeLabel: "",
      isShow: false, // 控制类目选择的显示与隐藏
      expenseCardList: [],
      cascaderProps: {
        value: "name",
        label: "name",
        children: "children",
      },
      isProcurementPlan: false,
      planData: [],
      nextObj: {
        projectInfo: {},
        planData: []
      },
      bizid: "",
      sureLoading: false
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 初始化方法
    init(amount) {
      this.needPurchaseDialogObj.procurementMethodName = ""
      if (!this.isProcurementPlan) {
        this.$set(this.needPurchaseDialogObj, "purchaseActuator", "");
        this.$set(this.needPurchaseDialogObj, "procurementMethodName", "");
      }
      // 初始化时获取采购标的的下拉数据
      this.$nextTick(() => {
        this.$refs.multiLevelSelector.getPurchaseClassificationVo({ amount, isGovPlan: this.isProcurementPlan })
      })
    },
    // 打开对话框
    openDialog(expenseCardList, needPurchaseDialogObj, isDraft=false) {
      this.sureLoading = false
      this.needPurchaseDialogObj = needPurchaseDialogObj;
      this.expenseCardList = JSON.parse(JSON.stringify(expenseCardList));
      this.isProcurementPlan = needPurchaseDialogObj.isProcurementPlan;
      this.planData = needPurchaseDialogObj.planData;
      let amount = 0;
      const expenseCardListArr = JSON.parse(JSON.stringify(expenseCardList));
      if (expenseCardListArr.length > 0) {
        amount = expenseCardListArr.reduce((sum, item) => {
          // 添加空值判断：如果 thisUsedAmount 不存在则视为0
          const currentApply = item.thisUsedAmount || "0.00";
          // 转换时使用当前处理的 currentApply 变量
          const mount = parseFloat(currentApply.replace(/,/g, ""));
          return sum + (isNaN(mount) ? 0 : mount); // 修正：检查 mount 而不是 amount
        }, 0);
        amount = this.$formatMoney(amount);
        this.$set(this.nextObj, 'amount',amount)
        this.$set(this.needPurchaseDialogObj, "amount", amount);
        this.$set(this.nextObj, "planData", this.planData);
        this.$set(this.nextObj, "expenseCardList", this.expenseCardList);
        this.$set(this.needPurchaseDialogObj, "isDraft", isDraft);

        if(!isDraft) {
          this.$set(this, 'bizid', '')
          this.$set(this.nextObj, 'isDraft', false)
        } else {

          this.$set(this, 'bizid', needPurchaseDialogObj.bizid)
          this.$set(this.nextObj, 'isDraft', true)
          this.$set(this.nextObj.projectInfo, 'bizid', needPurchaseDialogObj.bizid)
        }
        // 如果有采购计划 禁用采购执行机构、采购方式 并且取采购计划中的值进行赋值
        if (this.isProcurementPlan) {
          const {
            purchaseActuator,
            procurementMethodName,
            procurementMethodBizid,
          } = this.planData[0];
          this.$set(
            this.needPurchaseDialogObj,
            "purchaseActuator",
            purchaseActuator
          );
          this.$set(
            this.needPurchaseDialogObj,
            "procurementMethodName",
            procurementMethodName
          );
          this.$set(
            this.needPurchaseDialogObj,
            "procurementMethodBizid",
            procurementMethodBizid
          );
        }
      }
      this.dialogVisible = true;
        // 增加草稿箱功能
        if(!isDraft){
          this.init(amount);
        } else {
          this.$nextTick(() => {
            this.$refs.multiLevelSelector.getPurchaseClassificationVo({ amount, isGovPlan: this.isProcurementPlan })
            this.$refs.multiLevelSelector.setProjectDraft(needPurchaseDialogObj)
          })
        }
    },
    // 下一步
    async handleNext() {
        if (!this.bizid) {
          this.$message.error("请先保存项目信息！");
          return;
        }
      // 1.政府集采
      // 如果项目的采购方式为“公开招标、邀请招标、竞争性谈判、单一来源谈判、竞价采购方式”时，显示如下的采购需求编制界面
      let classificationFlagMap = {};
      let flags = [];
      let targetFlag = null;
      let { formData: { procurementMethodName, purchaseClassification, procurementCategory} } = await this.$refs.multiLevelSelector.getValidFormData()
      let joinedClassification = purchaseClassification
      this.$set(this.nextObj, "procurementCategory", procurementCategory)
      this.$set(this.nextObj.projectInfo, "selfPurchaseType", joinedClassification)
      this.getPurPorjectInfo(this.bizid);
      // 采购方式赋值
      this.$set(this.needPurchaseDialogObj, 'procurementMethodName', procurementMethodName)
      if (["公开招标", "邀请招标", "竞争性谈判", "单一来源谈判", "竞价"].includes(procurementMethodName)) {
        // 定义分类与标志位的映射关系
        classificationFlagMap = {
          "货物类-通用货物": "hwflag",
          "货物类-科研仪器设备": "sbflag",
          "货物类-文献资源": "hwflag",
          "工程类": "gcflag",
          "服务类-通用服务": "fwflag",
          "服务类-文献资源": "fwflag",
          "服务类-图书出版和论文发表": "fwflag",
        };
        // 先批量重置所有相关标志位为false
        flags = ["hwflag", "sbflag", "gcflag", "fwflag"];
        flags.forEach((flag) =>
          this.$set(this.needPurchaseDialogObj, flag, false)
        );

        // 根据分类设置目标标志位为true（存在则设置）
        targetFlag = classificationFlagMap[joinedClassification];
        if (targetFlag) {
          this.$set(this.needPurchaseDialogObj, targetFlag, true);
        }
        this.$set(this.needPurchaseDialogObj, "dzmcflag", false);
        this.$refs.fillProcurementDialog.openDialog(
          this.needPurchaseDialogObj,
          this.expenseCardList
        );
        return;
      }
      // 单独处理电子卖场（保持原有逻辑）
      if (procurementMethodName === "电子卖场") {
        this.$set(this.needPurchaseDialogObj, "dzmcflag", true);
        this.$set(this.needPurchaseDialogObj, "sbflag", false);
        this.$set(this.needPurchaseDialogObj, "fwflag", false);
        this.$set(this.needPurchaseDialogObj, "gcflag", false);
        this.$set(this.needPurchaseDialogObj, "hwflag", false);
        this.$refs.fillProcurementDialog.openDialog(
          this.needPurchaseDialogObj,
          this.expenseCardList,
        );
        return;
      }
      // 2.学校集采  两种情况  学校集中采购/社会代理机构
      //  学校集中采购
      if (["公开征集", "邀请竞标", "公开询价", "定向询价"].includes(procurementMethodName)) {
        // 定义分类与标志位的映射关系
        classificationFlagMap = {
          "货物类-通用货物": "hwflag",
          "货物类-科研仪器设备": "hwflag",
          "货物类-文献资源": "hwflag",
          "工程类": "gcflag",
          "服务类-通用服务": "fwflag",
          "服务类-文献资源": "fwflag",
          "服务类-图书出版和论文发表": "fwflag",
        };
        // 先批量重置所有相关标志位为false
        flags = ["hwflag", "sbflag", "gcflag", "fwflag"];
        flags.forEach((flag) =>
          this.$set(this.needPurchaseDialogObj, flag, false)
        );
        this.$set(this.needPurchaseDialogObj, "dzmcflag", false);
        // 根据分类设置目标标志位为true（存在则设置）
        targetFlag = classificationFlagMap[joinedClassification];
        if (targetFlag) {
          this.$set(this.needPurchaseDialogObj, targetFlag, true);
        }
        this.$refs.fillProcurementDialog.openDialog(
          this.needPurchaseDialogObj,
          this.expenseCardList
        );
        return;
      }
      //  社会代理机构
      if (["公开招标", "邀请招标", "竞争性谈判", "单一来源谈判"].includes(procurementMethodName)) {
        // 定义分类与标志位的映射关系
        classificationFlagMap = {
          "货物类-通用货物": "hwflag",
          "货物类-科研仪器设备": "hwflag",
          "货物类-文献资源": "hwflag",
          "工程类": "gcflag",
          "服务类-通用服务": "fwflag",
          "服务类-文献资源": "fwflag",
          "服务类-图书出版和论文发表": "fwflag",
        };
        // 先批量重置所有相关标志位为false
        flags = ["hwflag", "sbflag", "gcflag", "fwflag"];
        flags.forEach((flag) =>
          this.$set(this.needPurchaseDialogObj, flag, false)
        );

        // 根据分类设置目标标志位为true（存在则设置）
        targetFlag = classificationFlagMap[joinedClassification];
        if (targetFlag) {
          this.$set(this.needPurchaseDialogObj, targetFlag, true);
        }
        this.$set(this.needPurchaseDialogObj, "dzmcflag", false);
        this.$refs.fillProcurementDialog.openDialog(
          this.needPurchaseDialogObj,
          this.expenseCardList
        );
        return;
      }
      // 3.二级单位自采
      if (["E商城"].includes(procurementMethodName)) {
        this.$refs.chooseEmallDialog.openDialog(
          this.needPurchaseDialogObj,
          this.expenseCardList
        );
        this.$set(this.needPurchaseDialogObj, "dzmcflag", false);
        return;
      }
      if (["高校竞价网"].includes(procurementMethodName)) {
        this.$refs.chooseSchoolDialog.openDialog(
          this.needPurchaseDialogObj,
          this.expenseCardList
        );
        return;
      }
      if (["校内文件没有写采购方式","三方比价"].includes(procurementMethodName)) {
        this.$refs.chooseRecordDialog.openDialog(
          this.needPurchaseDialogObj,
          this.expenseCardList
        );
        this.$set(this.needPurchaseDialogObj, "dzmcflag", false);
        return;
      }
    },
    // 上一步按钮逻辑（可扩展）
    handlePrve() {
      this.dialogVisible = false;
      const needPurchaseDialogObj = {
        needPurchaseDialogObj: this.needPurchaseDialogObj,
        bizid: this.bizid,
        expenseCardList: this.expenseCardList,
      };
      this.$emit("handlePrve", needPurchaseDialogObj);
    },
    // 确定按钮逻辑（可扩展）
    async handleSure() {
      const { valid, formData } = await this.$refs.multiLevelSelector.getValidFormData()
      const obj = {
        purProjectInfoEntity: {
          ...formData,
          ...formData?.projectInfo,
          currentBidIndex:2,
          currentBidTitle: "填写、选择或确认采购信息",
          bizid: this.bizid
        },
        expenseCardList: this.expenseCardList,
      };
      this.sureLoading = true
      if (valid) {
        this.$callApi(
          "WFSAVE&dataType=PurProjectInfoEntity",
          obj,
          ({data}) => {
            if (data) {
              this.$message.success('保存成功!')
              this.sureLoading = false
              this.bizid = data.bizid;
              this.$set(this.nextObj.projectInfo,'bizid',this.bizid)
              this.getPurPorjectInfo(this.bizid);
              return true;
            }else {
              this.sureLoading = false
              this.$message.error('保存失败!')
            }
          },() => {
            this.sureLoading = false
          },
          { isSave: false }
        );
      }else{
        // this.$message.error('请填写必填信息!')
        this.sureLoading = false
      }
    },
    // 根据预算金额查询采购组织形式下拉框数据
    getProcurementCategoryByAmount(amount) {
      this.$callApi("getProcurementCategoryByAmount", { amount }, (result) => {
        this.organizationFormOptions = result.data;
        if (this.organizationFormOptions.length === 1) {
          this.needPurchaseDialogObj.procurementCategory =
            this.organizationFormOptions[0]?.tvalue;
          this.getPurchaseClassificationVo(
            amount,
            this.needPurchaseDialogObj.isProcurementPlan
          );
        }
        return true;
      });
    },
    // 采购组织形式触发事件
    handleChangeCategory(value) {
      if (this.isProcurementPlan) {
        return;
      }
    },
    // 根据预算金额和采购组织形式查询采购标的下拉框数据
    getPurchaseClassificationVo(amount, isGovPlan) {
      this.$callApi(
        "listPurchaseClassification",
        {
          amount,
          isGovPlan,
        },
        ({ data }) => {
          this.targetTypeOptions = this.cleanEmptyChildren(data);
          return true;
        }, () => {},
        { isSave: false }
      );
    },
    // 采购标的触发事件
    handleChangePurchaseType(value) {
      this.typeLabel = value.join("-");
      if (this.isProcurementPlan) {
        return;
      }
      this.listSelfPurchaseDescription();
    },
    // 查询自行采购描述（选择采购标的后请求该接口，
    listSelfPurchaseDescription() {
      let { purchaseClassification, procurementCategory } =
        this.needPurchaseDialogObj;
      this.$callApi(
        "listSelfPurchaseDescription",
        {
          amount: this.needPurchaseDialogObj.amount,
          selfPurchaseType: purchaseClassification?.join("-"),
          procurementCategory: procurementCategory,
        },
        ({ data }) => {
          this.isShow = data.isShow;
          if (!data.isShow) {
            // 没有类目 直接查采购方式和机构接口
            this.$set(this.needPurchaseDialogObj, "selfPurchaseDescription", "");
            this.getProcurementMethodAndPurchaseActuator(data.isShow);
          }
          if (this.isShow) {
            if (!this.isProcurementPlan) {
              this.$set(this.needPurchaseDialogObj, "purchaseActuator", "");
              this.$set(
                this.needPurchaseDialogObj,
                "procurementMethodName",
                ""
              );
            }
          } else {
            if (!this.isProcurementPlan) {
              this.needPurchaseDialogObj.purchaseActuator =
                this.purchaseActuatorEntityList[0]?.purchaseActuatorName;
            }
          }
          this.documentProjectOptions =
            data.descriptions.map((desc) => ({
              label: desc, // 显示文本使用原字符串
              value: desc, // 值使用原字符串（也可以根据需求改为索引等）
            })) || [];
          return true;
        },
        () => {}
      );
    },
    // 类目类型事件
    handleDescriptionChange(value) {
      // 如果有类目 选完类目再去调直接查采购方式和机构接口
      // 有类目清空旧数据
      this.$set(this.needPurchaseDialogObj,'selfPurchaseDescription',value)
      if (!this.isProcurementPlan) {
        this.$set(this.needPurchaseDialogObj, "purchaseActuator", "");
        this.$set(this.needPurchaseDialogObj, "procurementMethodName", "");
        this.$set(this.needPurchaseDialogObj, "procurementMethodBizid", "");
      }
      this.purchaseActuatorEntityList = [];
      this.settingProcurementMethodEntityList = [];
      this.getProcurementMethodAndPurchaseActuator();
    },
    // 采购执行机构下拉数据
    getProcurementMethodAndPurchaseActuator(isShow = false) {
      let {
        amount,
        procurementCategory,
        purchaseClassification,
        selfPurchaseDescription,
      } = this.needPurchaseDialogObj;
      this.$callApi(
        "getProcurementMethodAndPurchaseActuator",
        {
          amount,
          procurementCategory,
          selfPurchaseType: purchaseClassification?.join("-"),
          description: selfPurchaseDescription,
        },
        ({ data }) => {
            const {
            purchaseActuatorEntityList = [],
            settingProcurementMethodEntityList = [],
          } = data || {};  // Add fallback to empty object
          this.purchaseActuatorEntityList = purchaseActuatorEntityList || [];
          if (
            this.purchaseActuatorEntityList.length === 1 &&
            !this.isProcurementPlan
          ) {
            this.needPurchaseDialogObj.purchaseActuator =
              this.purchaseActuatorEntityList[0]?.purchaseActuatorName;
          } else {
            this.$set(this.needPurchaseDialogObj, "purchaseActuator", "");
          }
          if (
            this.settingProcurementMethodEntityList.length === 1 &&
            !this.isProcurementPlan
          ) {
            // this.needPurchaseDialogObj.procurementMethodName =
            //   this.settingProcurementMethodEntityList[0]?.name;
            const name = this.settingProcurementMethodEntityList[0]?.name;
            this.$set(
              this.needPurchaseDialogObj,
              "procurementMethodName",
              name
            );
          }
          this.settingProcurementMethodEntityList =
            settingProcurementMethodEntityList || [];
          return true;
        },
        () => {},
        { isSave: false }
      );
    },
    // 递归清理空的子节点
    cleanEmptyChildren(data) {
      return data.map((item) => {
        const newItem = { ...item };
        if (newItem.children && newItem.children.length > 0) {
          newItem.children = this.cleanEmptyChildren(newItem.children);
        } else {
          newItem.children = undefined;
        }
        return newItem;
      });
    },
    // 新增：处理横杠的工具方法
    getAfterHyphen(str) {
      if (!str || typeof str !== "string") return ""; // 空值/非字符串处理
      const splitArr = str.split("-");
      return splitArr.length > 1 ? splitArr[splitArr.length - 1] : str;
    },
    handlePurchaseMethodChange(value) {
      const obj =
        this.settingProcurementMethodEntityList.find(
          (item) => item.name === value
        ) || "";
      this.$set(this.needPurchaseDialogObj, "procurementMethodName", obj.name);
      this.$set(
        this.needPurchaseDialogObj,
        "procurementMethodBizid",
        obj.bizid
      );
    },
    handlePrveFillProcurementDialog(obj) {
      this.dialogVisible = true;
      this.needPurchaseDialogObj = obj.needPurchaseDialogObj;
      this.expenseCardList = obj.expenseCardList;
    },
    // 获取项目信息
    getPurPorjectInfo(bizid) {
      this.$callApiParams(
        "getBasicPurProjectInfoVo",
        {
          bizid,
        },
        ({ data }) => {
          const {
            purProjectInfo,
            expenseCardEntityList,
            procurementPlanEntityList,
          } = data;
          this.nextObj = {
            projectInfo: purProjectInfo,
            procurementPlanEntity: procurementPlanEntityList,
            expenseCardList: expenseCardEntityList,
          };
          this.needPurchaseDialogObj = {
            ...this.needPurchaseDialogObj,
            ...purProjectInfo
          }
          // 处理反复切换 金额丢失问题
          const budgetAmount = this.$formatMoney(purProjectInfo.budgetAmount)
          this.$set(this.needPurchaseDialogObj, "amount", budgetAmount);
          return true;
        }
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.el-cascader {
  width: 100%;
}
::v-deep {
  .el-dialog__body {
    padding-top: 10px!important;
  }
}
</style>
