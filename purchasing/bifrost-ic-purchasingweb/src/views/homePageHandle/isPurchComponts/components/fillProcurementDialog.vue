<!--
 * @Description: 2 选择经费卡
 * @Version: 2.0
 * @Autor: 作者姓名
 * @Date: 创建日期
 * @LastEditors: G<PERSON><PERSON>ey
 * @LastEditTime: 2025-09-15 10:22:00
-->
<template>
  <div>
    <el-dialog
      :title="homeTitle"
      :visible.sync="dialogVisible"
      width="80%"
      :before-close="handleClose"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      v-if="dialogVisible"
    >
    <template #title>
        <div class="text-left text-base text-[#000] font-semibold">
          <span>{{ homeTitle }}</span>
        </div>
      </template>
      <customSteps 
          :active="3"
         />
      <div class="content" style="overflow: overlay; height: 70vh">
        <!-- 需求 -->
        <!-- 货物类 -->
        <goodsPurchaseRequirement
          v-if="hwflag"
          :nextObj="nextObj"
          :procurementMethodName="procurementMethodName"
          ref="goodsPurchaseRequirement"
          @updateLoading="updateLoading"
        ></goodsPurchaseRequirement>
        <!-- 工程类 -->
        <engineeringPurchaseRequirement
          v-if="gcflag"
          :procurementMethodName="procurementMethodName"
          :nextObj="nextObj"
          ref="engineeringPurchaseRequirement"
          @updateLoading="updateLoading"
        />
        <!-- 服务类 -->
        <servicePurchaseRequirement
          v-if="fwflag"
          :procurementMethodName="procurementMethodName"
          :nextObj="nextObj"
          ref="servicePurchaseRequirement"
          @updateLoading="updateLoading"
        />
        <!-- 设备类 -->
        <devicePurchaseRequirement
          :nextObj="nextObj"
          :procurementMethodName="procurementMethodName"
          v-if="sbflag"
          ref="devicePurchaseRequirement"
          @updateLoading="updateLoading"
        />
        <!-- 电子卖场 -->
        <electronicStore
         v-if="dzmcflag" 
         ref="electronicStore"
         :nextObj="nextObj"
         @updateLoading="updateLoading"
        ></electronicStore>
      </div>
      <div slot="footer" class="dialog-footer text-center">
        <el-button @click="handlePrve">上一步</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="sureLoading">保存</el-button>
        <!-- <el-button type="primary" @click="handleAudit">提交审核</el-button> -->
        <el-button @click="handleNext">下一步</el-button>
      </div>
    </el-dialog>

    <!-- 第四页面 -->
    <fourthViewDialog
      ref="fourthViewDialog"
      :nextObj="nextObj"
      @closeDialog="closeDialog"
      @handlePrveforthViewDialog="handlePrveforthViewDialog"
    />
     <Middleware ref="Middleware"/>
  </div>
</template>
<script>
// 
import fourthViewDialog from "./fourthViewDialog";
export default {
  name: "fillProcurementDialog",
  components: { fourthViewDialog },
  props: {
    nextObj: {
      type: Object,
      required: true,
    }
  },
  data() {
    return {
      dialogVisible: false, // 控制对话框的显示与隐藏
      homeTitle: "我要采购-编写采购需求", // 对话框标题
    //   有计划
      sbflag: false, // 设备类
      fwflag: false, // 服务类
      gcflag: false, // 工程类
      hwflag: false, // 货物类
      dzmcflag: false, // 电子卖场
    //   无计划
      needPurchaseDialogObj: {}, // 上一个组件传过来的数据
      expenseCardList: [], // 经费卡列表
      procurementMethodName: "",
      sureLoading: false,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 打开对话框
    openDialog(needPurchaseDialogObj, expenseCardList) {
      this.needPurchaseDialogObj = needPurchaseDialogObj;
      this.expenseCardList = expenseCardList;
      this.procurementMethodName = needPurchaseDialogObj.procurementMethodName;
      // 优化：直接同步标志位（根据上一步逻辑，这些标志位互斥，只需直接赋值）
      // 采购需求
      this.sbflag = needPurchaseDialogObj?.sbflag;
      this.fwflag = needPurchaseDialogObj?.fwflag;
      this.gcflag = needPurchaseDialogObj?.gcflag;
      this.hwflag = needPurchaseDialogObj?.hwflag;
      this.dzmcflag = needPurchaseDialogObj?.dzmcflag;
      //
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.executeComponentMethod('getAllData')
        this.sureLoading = false
      })
    },
    updateLoading() {
      this.sureLoading = false
    },
    closeDialog() {
      this.dialogVisible = false;
      this.$emit("closeDialog");
    },
    // 公共方法：执行对应组件的指定方法
    executeComponentMethod(methodName) {
      const componentMap = {
        hwflag: { ref: 'goodsPurchaseRequirement' },  // 货物类对应组件
        sbflag: { ref: 'devicePurchaseRequirement' }, // 设备类对应组件
        fwflag: { ref: 'servicePurchaseRequirement' },// 服务类对应组件
        gcflag: { ref: 'engineeringPurchaseRequirement' }, // 工程类对应组件
        dzmcflag: { ref: 'electronicStore' },  //电子卖场类对应组件
      };

      // 使用for...in替代Object.entries（兼容旧环境）
      for (const flag in componentMap) {
        if (componentMap.hasOwnProperty(flag)) { // 防止遍历到原型链属性
          const { ref } = componentMap[flag];
          // 使用普通function替代箭头函数（兼容旧环境）
          if (this[flag] && this.$refs[ref]) {
            this.$refs[ref][methodName](); // 调用组件方法
            this.sureLoading = true
          }
        }
      }
    },

    // 保存按钮逻辑
    handleSubmit() {
      this.executeComponentMethod('handleSubmit');
    },

    // 提交审核逻辑
    handleAudit() {
      this.executeComponentMethod('handleAudit');
    },
    async handlePrve() {
      const needPurchaseDialogObj = {
        needPurchaseDialogObj: this.needPurchaseDialogObj,
        expenseCardList: this.expenseCardList,
      };
      if(this.nextObj?.isDraft){
        const projectData = await this.getPurPorjectInfo(this.nextObj.projectInfo.bizid); // 获取项目信息
        const { expenseCardList, procurementPlanEntity,projectInfo, isProcurementPlan } = projectData;
      const needPurchaseDialogObj = {
        isProcurementPlan,
        planData: procurementPlanEntity,
        projectInfo
      };
      const {
        purProjectInfoName,
        procurementCategory,
        selfPurchaseDescription,
        purchaseActuator,
        procurementMethodBizid,
        purchaseClassification,
        bizid
       } = projectInfo
      needPurchaseDialogObj.purProjectInfoName = purProjectInfoName;
      needPurchaseDialogObj.bizid = bizid
      needPurchaseDialogObj.procurementCategory = procurementCategory
      needPurchaseDialogObj.selfPurchaseDescription = selfPurchaseDescription
      needPurchaseDialogObj.purchaseActuator =  purchaseActuator
      needPurchaseDialogObj.procurementMethod =  procurementMethodBizid
      needPurchaseDialogObj.isCustomType = purchaseClassification
        this.$refs.Middleware.openDialog(
            expenseCardList,
            needPurchaseDialogObj,
            true
        );
        this.dialogVisible = false;
        return
      }
      this.dialogVisible = false;
      this.$emit("handlePrveFillProcurementDialog", needPurchaseDialogObj);
    },
    getPurPorjectInfo(bizid) {
      return new Promise((resolve, reject) => {
        this.$callApiParams(
          "getBasicPurProjectInfoVo",
          {
            bizid,
          },
          ({ data }) => {
            const {
              purProjectInfo,
              expenseCardEntityList,
              procurementPlanEntityList,
              isShowPlan
            } = data;
            const amount = this.$formatMoney(purProjectInfo.budgetAmount);
            const result = {
              projectInfo: purProjectInfo,
              procurementPlanEntity: procurementPlanEntityList,
              expenseCardList: expenseCardEntityList,
              amount,
              isProcurementPlan: isShowPlan
            };
            // 在子组件中
            this.$emit('update:nextObj', {
              projectInfo: purProjectInfo,
              procurementPlanEntity: procurementPlanEntityList,
              expenseCardList: expenseCardEntityList,
            });
            resolve(result);
            return true;
          },
          (error) => {
            reject(error);
            return true;
          }
        ,() => {}, { isSave: false });
      });
    },
    handleNext() {
      this.$callApiParams('checkThirdlyNodeIsSave', {
        bizid: this.nextObj.projectInfo.bizid,
      }, res => {
        if (!res.data) {
          this.$message.error("请先保存项目信息！");
          return true
        }
        this.dialogVisible = false;
        const needPurchaseDialogObj = {
          needPurchaseDialogObj: this.needPurchaseDialogObj,
          expenseCardList: this.expenseCardList,
        };
        this.$refs.fourthViewDialog.openDialog(needPurchaseDialogObj);
        return true
      }, (result) => {
        return true
      })
    },
    handlePrveforthViewDialog()  {
        this.dialogVisible = true
    },
    // 关闭对话框
    handleClose() {
      this.closeDialog();
    },
    // 确定按钮逻辑（可扩展）
    handleSure() {
      // 自定义确定操作逻辑
      this.dialogVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep {
  .el-dialog__body {
    padding-top: 10px!important;
  }
}
</style>
