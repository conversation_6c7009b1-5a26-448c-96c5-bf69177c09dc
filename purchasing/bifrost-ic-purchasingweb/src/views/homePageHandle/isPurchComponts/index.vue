<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-02 14:11:27
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-27 09:10:55
-->
<template>
  <div class="">
    <el-card body-style="padding: 10px;">
      <tipsDialog ref="tipsDialog" @tipsSure="tipsSure" />
      <chooseExpenseCardDialog ref="chooseExpenseCardDialog"  />
      <pur-project-info-expense-card-dialog
      :showTips="true"
      @openinfoDialog="openinfoDialog"
      ref="expenseCardDialogRef"
      customApi="getProcurementPlanByExpenseCardBizid"
    />
    </el-card>
  </div>
</template>

<script>
import tipsDialog from "./components/tipsDialog.vue";
import chooseExpenseCardDialog from "./components/chooseExpenseCardDialog.vue";
import purProjectInfoExpenseCardDialog from "./components/chooseExpenseCardDialogNew.vue";
export default {
  name: "isPurchPage",

  components: {
    tipsDialog,
    chooseExpenseCardDialog,
    purProjectInfoExpenseCardDialog
  },
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {
  },
  created() {},
  mounted() {},
  methods: {
    showDialog() {
      this.$refs.tipsDialog.showDialog();
    },
    // 提示框确定
    tipsSure(isReadOnly) {
      //   弹出选择经费卡选择框
      this.$refs.expenseCardDialogRef.openDialog({
        needExpenseCard: true,
        textType: "新增",
        bizids: "",
        isReadOnly: isReadOnly === 0 ? true : false,
      });
      // this.$refs.chooseExpenseCardDialog.showDialog();
    },
    openinfoDialog(v) {
      this.$refs.chooseExpenseCardDialog.showDialog(v);
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
