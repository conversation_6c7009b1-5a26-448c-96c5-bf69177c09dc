<template>
  <div id="form-datail-info" style="height: 100%;display: flex;flex-direction: column;">
    <div style="height: 40%;" v-if="isShowBa">
      <b-curd ref="curdListBa"/>
    </div>
    <b-curd ref="curdListPlan"/>
  </div>
</template>

<script>
import $ from 'jquery'

export default {
  name: 'formDetail采购需求单-指标与采购品目-4',
  data() {
    return {
      purId: '',
      isShowBa: true
    }
  },

  methods: {
    init(dlg, params) {
      params.dataVo.colItems.forEach(colItem => {
        if (colItem.labelOrigin === '采购单类型') {
          if (colItem.dataValue === '采购需求-不关联指标') {
            this.isShowBa = false
            if (params.dataVo.extData.setEditTabLabel) {
              params.dataVo.extData.setEditTabLabel('formDetail采购需求单-指标与采购品目-4', '采购品目')
            }
          } else {
            this.isShowBa = true
            if (params.dataVo.extData.setEditTabLabel) {
              params.dataVo.extData.setEditTabLabel('formDetail采购需求单-指标与采购品目-4', '指标与采购品目')
            }
          }
          return
        }
      })
      // 审核扩展params为true
      if (params === true) {
        this.purId = dlg.baId
      } else {
        this.purId = params.billId
        $('.form-datail').css({ height: '33%' })
      }
      this.initCurd()
    },
    initCurd() {
      if (!this.purId) {
        this.purId = '0'
      }
      const _this = this
      if (this.isShowBa) {
        this.$refs.curdListBa.init({
          params: {
            dataApiKey: 'selectPurRelevantBaPageData',
            CFORM_ID_in: this.purId
          },
          showPager: false,
          hideCurdButton: ['新增', '修改', '详情', '删除'],
          getSummaries(param) {
            const { columns, data } = param
            const sums = []
            if (columns.length<1) {
              return sums
            }
            columns.forEach((column, index) => {
              if (column.label !== undefined && column.label.indexOf('金额') !== -1 || column.label === '单价' || column.label === '数量') {
                const values = data.map(item => Number(item[column.property]))
                if (!values.every(value => isNaN(value))) {
                  sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr)
                    if (!isNaN(value)) {
                      return prev + curr
                    } else {
                      return prev
                    }
                  }, 0)
                  if (column.label !== '数量') {
                    sums[index] = _this.$formatMoney(sums[index])
                  }
                } else {
                  sums[index] = ''
                }
              } else {
                sums[index] = ''
              }
            })
            sums[1] = '合计'
            return sums
          }
        })
        this.$refs.curdListBa.setButtonBarVisible(false, true)
        this.$refs.curdListBa.setNoPager()
      }

      this.$refs.curdListPlan.init({
        params: {
          dataApiKey: 'selectPurItemPageData',
          PUR_ID_in: this.purId,
          isShowBa: this.isShowBa
        },
        showPager: false,
        hideCurdButton: ['新增', '修改', '详情', '删除'],
        getSummaries(param) {
          const { columns, data } = param
          const sums = []
          if (columns.length<1) {
            return sums
          }
          columns.forEach((column, index) => {
            if (column.label !== undefined && column.label.indexOf('金额') !== -1 || column.label === '单价' || column.label === '数量') {
              const values = data.map(item => Number(item[column.property]))
              if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                  const value = Number(curr)
                  if (!isNaN(value)) {
                    return prev + curr
                  } else {
                    return prev
                  }
                }, 0)
                if (column.label !== '数量') {
                  sums[index] = _this.$formatMoney(sums[index])
                }
              } else {
                sums[index] = ''
              }
            } else {
              sums[index] = ''
            }
          })
          sums[1] = '合计'
          return sums
        }
      })
      this.$refs.curdListPlan.setButtonBarVisible(false, false)
      this.$refs.curdListPlan.setNoPager()
    }
  }
}
</script>

<style lang="scss">

</style>
