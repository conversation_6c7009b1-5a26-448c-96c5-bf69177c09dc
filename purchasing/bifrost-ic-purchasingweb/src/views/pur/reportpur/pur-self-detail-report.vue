<template>
  <div>
    <b-report ref="curdList"></b-report>
    <purpolict-dialog-list ref="purPolicyDialogList"/>
  </div>
</template>

<script>
export default {
  name: 'pur-self-detail-report',
  provide() {
    return {
      basePageInited: this.basePageInited
    }
  },
  data() {
    return {
      enterpriseName: '',
      setYears: '',
      conType: [],
      deptParams: '部门',
      treeDepData: [],
      demosetting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'name'
          }
        }
      }
    }
  },
  mounted() {
    Promise.all(this.$createPromise(this.selectAccSets, this.getBooksetName)).then(() => {
      this.init()
    })
  },
  methods: {
    init() {
      const searchForm = [
        '采购金额:DEAL_AMOUNT:金额筛选',
        '采购时间:采购时间:日期区间',
        '采购部门:PUR_DEPARTMENT_like:树:#' + JSON.stringify(this.treeDepData) + ':##' + JSON.stringify(this.demosetting),
        '项目名称:PUR_DETAIL_NAME_like:文本']
      var initParams = {}
      var param = {
        dataApiKey: 'getSelfDetailData',
        PUR_TYPE_eq: '自行采购'
      }
      initParams.params = param
      initParams.searchForm = searchForm
      initParams.enterpriseName = this.enterpriseName
      initParams.reportTitle = this.setYears + '年新桥街道自行采购情况表'
      initParams.hiddenButtons = ['新增', '修改', '删除']
      initParams.btDetailClick = { click: row => this.viewDetails(row, '双击') }
      initParams.buttons = [
        { text: '详情', icon: 'el-icon-document', enabledType: '1', click: bt => this.viewDetails(bt, '按钮') },
        { text: '审核历史', icon: 'el-icon-time', enabledType: '1', click: bt => this.isShowWfHistory(bt) }
      ]
      initParams.rowCheckedCallback = rows => {
        if (this.$isEmpty(rows[0].id)) {
          this.$call(this, 'basePage', 'setBtProperty', '详情', 'disabled', true)
          this.$call(this, 'basePage', 'setBtProperty', '审核历史', 'disabled', true)
        }
      }
      this.$refs.curdList.init(initParams)
    },
    reload() {
      this.$reInit(this)
    },
    viewDetails(row, text) {
      if (text === '按钮') {
        if (this.$isEmpty(row.params.rows[0].purPolicyId)) {
          return this.$message.warning('合计行或自行采购决策主表无法查看详情')
        }
        this.$nextTick(() => {
          this.$callApiParams('selectPurPolicyVo',
            { bizzId: row.params.rows[0].purPolicyId }, result => {
              this.PurPolicyVo = result.data

              var data2 = {
                purPolicyEntity: this.PurPolicyVo.purPolicyEntity,
                purPolicyDetailEntities: this.PurPolicyVo.purPolicyDetailEntities,
                purSupplierDetailEntities: this.PurPolicyVo.purSupplierDetailEntities,
                attList: this.PurPolicyVo.attList,
                extData: { attData: result.data.auditAttList },
                data: { id: this.PurPolicyVo.purPolicyEntity.id }
              }
              this.$refs.purPolicyDialogList.init(data2)

              return true
            })
          // this.$refs.purpolicyEdit.init(row.ID)
        })
      }
      if (text === '双击') {
        if (this.$isEmpty(row.purPolicyId)) {
          return this.$message.warning('合计行或自行采购决策主表无法查看详情')
        }
        this.$nextTick(() => {
          this.$callApiParams('selectPurPolicyVo',
            { bizzId: row.purPolicyId }, result => {
              this.PurPolicyVo = result.data
              var data2 = {
                purPolicyEntity: this.PurPolicyVo.purPolicyEntity,
                purPolicyDetailEntities: this.PurPolicyVo.purPolicyDetailEntities,
                purSupplierDetailEntities: this.PurPolicyVo.purSupplierDetailEntities,
                attList: this.PurPolicyVo.attList,
                extData: { attData: result.data.auditAttList },
                data: { id: this.PurPolicyVo.purPolicyEntity.id }
              }
              this.$refs.purPolicyDialogList.init(data2)

              return true
            })
          // this.$refs.purpolicyEdit.init(row.ID)
        })
      }
    },

    // viewDetails(row, type) {
    //   if (type === '双击') {
    //     this.isSelect(row.purId,row.metaName)
    //   } else {
    //     this.isSelect(row.params.rows[0].purId,row.params.rows[0].metaName)
    //   }
    // },
    // isSelect(id,name) {
    //   if (this.$isEmpty(id)) {
    //     return this.$message.warning('合计行无法查看详情')
    //   } else {
    //     this.$showDetail(id, '采购申请单',undefined,name)
    //   }
    // },
    isShowWfHistory(bt) {
      if (this.$isEmpty(bt.getRowValue('purPolicyId'))) {
        return this.$message.warning('合计行无法查看审核历史')
      } else {
        var id = bt.getRowValue('purPolicyId')
        this.$showWfHistory(id, '', '', 'wfActionPurPolicyEntity')
      }
    },
    selectAccSets(resolve) {
      this.$callApi(
        'selectAccSets',
        '',
        result => {
          this.setYears = result.data.setYears
          resolve()
          return true
        },
        () => {
          resolve()
        }
      )
    },
    getBooksetName(resolve) {
      // 获取编制单位
      this.$callApi(
        'getBooksetName',
        '',
        result => {
          this.enterpriseName = result.data.booksetName
          resolve()
          return true
        },
        () => {
          resolve()
        }
      )
    },
    basePageInited(_this) {
      // 申报部门
      this.$callApiParams('getDept',
        { formType: this.deptParams }, result => {
          this.treeDepData = []
          result.data.forEach(re => {
            this.treeDepData.push({
              id: re.eleId,
              name: re.eleCode + ' ' + re.eleName,
              parentId: re.parentId,
              code: re.eleCode })
          })
          _this.updateTree && _this.updateTree(['采购部门:PUR_DEPARTMENT_like:树:#' + JSON.stringify(this.treeDepData) + ':##' + JSON.stringify(this.demosetting)])
          return true
        })
    }
  }
}
</script>

<style scoped>

</style>
