<template>
  <div>
    <b-curd ref="curdList"/>
  </div>
</template>

<script>
export default {
  name: 'gov-pur-pay',
  data() {
    return {
      purMode: [], // 采购方式
      booksetName: '',
      fiscalYear: '',
      agencyCode: '',
      mofDivCode: ''
    }
  },
  mounted() {
    Promise.all(this.$createPromise(this.getBooksetName)).then(() => {
      this.init()
    })
  },
  methods: {
    init() {
      var initParams = {}
      initParams.params = { dataApiKey: 'selectPurDataSwapSyncPageData' }
      initParams.hideCurdButton = ['新增', '修改', '删除', '详情'] // 不显示这些默认按扭
      initParams.buttons = [
        { text: '同步至采购系统', icon: 'el-icon-document', enabledType: '0', click: bt => this.syncToPurSystem(bt) },
        { text: '财政发送状态', icon: 'el-icon-document', enabledType: '1', click: bt => this.syncSendStatus(bt) },
        { text: '财政支付状态', icon: 'el-icon-document', enabledType: '1', click: bt => this.syncPayStatus(bt) },
        { text: '同步台账', icon: 'el-icon-document', enabledType: '1', click: bt => this.syncLedger(bt) },
        { text: '采购支付单结果', icon: 'el-icon-document', enabledType: '0', click: bt => this.bbb(bt) },
        { text: '采购支付结果统计', icon: 'el-icon-document', enabledType: '0', click: bt => this.ccc(bt) }
      ]
      this.$refs.curdList.init(initParams)
    },
    bbb(bt) { // 临时
      var rows = bt.params.rows
      // 组织数据
      const paramsData = this.$paramsData(rows, '同步', this.agencyCode, this.fiscalYear, this.mofDivCode)
      paramsData.page = 1
      paramsData.size = 10
      this.$api('/bifrost/gppaymentdual/v1/page', paramsData)
    },
    ccc(bt) { // 临时
      var rows = bt.params.rows
      // 组织数据
      const paramsData = this.$paramsData(rows, '同步', this.agencyCode, this.fiscalYear, this.mofDivCode)
      paramsData.page = 1
      paramsData.size = 10
      this.$api('/bifrost/gppayresultstatistics/v1/page', paramsData)
    },
    syncToPurSystem(bt) {

    },
    syncSendStatus(bt) {

    },
    syncPayStatus(bt) {

    },
    syncLedger(bt) {

    },
    getBooksetName(resolve) {
      // 获取编制单位
      this.$callApi(
        'getSetOfBooks',
        '',
        result => {
          this.booksetName = result.data.booksetName
          this.fiscalYear = result.data.fiscalYear
          this.agencyCode = result.data.agencyCode
          this.mofDivCode = result.data.mofDivCode
          resolve()
          return true
        },
        error => {
          resolve()
        }
      )
    }
  }
}
</script>

<style scoped>

</style>
