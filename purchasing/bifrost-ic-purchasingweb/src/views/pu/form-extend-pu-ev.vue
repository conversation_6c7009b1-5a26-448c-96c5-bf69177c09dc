<template>
  <span style="display: none"></span>
</template>
<script>
export default {
  name: 'formExtend-采购履约评价',
  data() {
    return {
    }
  },
  methods: {
    initFormExtend(formType, formFormat) {
      formFormat.addColItemModifiedCallbacks({
        '下拉框改变处理': (theColItem) => {
          this.refLabelAfter(theColItem, formFormat)
        }
      })
    },
    setExParams(params, colItem, dataVo) {
      const colItems = dataVo?.colItems
      const puIdColItems = colItems.filter(data => data.labelOrigin === '关联采购申请ID')
      const puId = this.$getRealValueRefID(puIdColItems[0].dataValue)
      if (colItem.labelOrigin === '履约供应商') {
        params['采购履约评价选择供应商'] = ''
        params.puId = puId
      }
    },
    isRelatedRefIncludeDept(item) { // 参照时是否填充关联部门
      if (item.labelOrigin === '关联采购申请') {
        return true
      }
      return false
    },
    refLabelAfter(theColItem, formFormat) {
      if (theColItem.labelOrigin === '履约供应商') {
        const labelValues = theColItem.labelValues
        const labelValue = labelValues.filter(data => data.value === theColItem.dataValue)[0]
        if (labelValue?.exData?.supplier?.bidAmount) {
          formFormat.setValue('成交金额', labelValue?.exData?.supplier?.bidAmount)
        } else {
          formFormat.setValue('成交金额', '')
        }
      }
    }
  }
}
</script>
