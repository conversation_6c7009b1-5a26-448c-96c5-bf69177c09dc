<template>
  <!-- 商城/框架协议采购结果  -->
  <div class="mallApply">
    <b-curd ref="curdList" />
    <editMall ref="editMall" @init="init"></editMall>
    <importRecordDialog ref="importRecordDialog"></importRecordDialog>
    <viewDialog ref="viewDialog"></viewDialog>
  </div>
</template>

<script>
import editMall from "@/views/shoppingMall/mallApply/components/editMall.vue";
import importRecordDialog from "./components/importRecordDialog.vue"
import viewDialog from "./components/viewDialog.vue"

export default {
  name: "mallPurch",
  components: {
    editMall,
    importRecordDialog,
    viewDialog
  },
  mounted() {
    this.init();
  },
  data() {
    return {};
  },
  methods: {
    init() {
      let initParams = {};
      (initParams.params = {
        dataApiKey: "selectMallFrameworkPageData",
        deleteApiKey: "deleteMiniPurchaseResultsRecord",
      }),
        (initParams.searchForm = [
          '采购进度:PROGRESS_like:文本',
          "采购表编号:RECORD_NUMBER_like:文本",
          "采购表名称:PROJECT_NAME_like:文本",
          "商城名称:MALL_NAME_eq:文本",
          "采购部门:DEPT_NAME_like:文本",
          "项目经办人:PROJECT_MANAGER_NAME_like:文本",
        ]);
      initParams.searchFormNum = 4;
      initParams.isShowOrderNumber = true;
      initParams.params.type = 3;
      initParams.hideCurdButton = ["新增", "修改", "详情", "删除"];
      initParams.params.pageRoute = this.$getRouter();
      initParams.buttons = [
        {
          text: "登记商城/框架协议平台订单号",
          icon: "el-icon-document-add",
          enabledType: "1",
          mainButton: true,
          click: (row) => {
            const { bizid, recordState } = row.getRowData();
            this.$refs.editMall.handleOpen(
              "view",
              "登记商城/框架协议平台订单号",
              bizid,
              true
            );
          },
        },
        {
          text: "导入网上商城订单",
          icon: "el-icon-upload2",
          enabledType: "0",
          mainButton: true,
          click: (row) => {
            const apiKey= 'template/网上商城订单导入模板.xls'
            const fileName = "网上商城订单导入模板.xls";
            const tableColumn = []
            this.$showImportExcelDlg(apiKey, fileName, tableColumn, {
              type: 0,
              onSuccess: () => {
                this.init()
              } })
          },
        },
        {
          text: "导入框架协议平台订单",
          icon: "el-icon-upload2",
          enabledType: "0",
          mainButton: true,
          click: (row) => {
            const apiKey= 'template/框架协议平台订单导入模板.xls'
            const fileName = "框架协议平台订单导入模板.xls";
            const tableColumn = []
            this.$showImportExcelDlg(apiKey, fileName, tableColumn, {
              type: 0,
              onSuccess: () => {
                this.init()
              } })
          },
        },
        {
          text: "查看导入记录",
          icon: "el-icon-document",
          enabledType: "0",
          click: (row) => {
            this.$refs.importRecordDialog.handleOpen(row, '导入记录')
          },
        },
        {
          text: "查看机器人采集情况",
          icon: "el-icon-view",
          enabledType: "1",
          click: (row) => {
            this.$refs.viewDialog.handleOpen("机器人采集情况")
          },
        },
      ];
      initParams.rowCheckedCallback = (rows) => {};
      this.$refs.curdList.init(initParams);
    },
    /**
     * 送审
     * @param row
     */
    submit(row) {
      const state = row?.getRowData().status;
      const rows = row.params.rows;
      if (["审核中"].includes(state)) {
        this.$message({
          message: "送审失败！您选择的项目正在审核中，不能重复提交审核！",
          type: "warning",
        });
        return;
      }
      if (state === "审核不通过") {
        this.$message.error(
          "送审失败！您选择的项目已完成审核，并且审核不通过，可以按照审核人员提出的审核意见完成修改后重新送审！"
        );
        return;
      }
      if (state === "审核通过") {
        this.$message.error(
          "送审失败！您选择的项目已审核通过，无需再次提交审核！"
        );
        return;
      }
      let params = {
        ids: row.getRowData().ID,
        dataType: "MallFrameworkEntity",
        apiKey: "WFSUBMIT",
      };
      this.$callApiParams("WFSUBMIT", params, (result) => {
        if (result.success) {
          this.$message({
            message: "送审成功",
            type: "success",
          });
          this.init();
        }
        return true;
      });
    },

    /**
     * 撤回
     */
    withdrawSubmit(row) {
      let message = "";
      const { recordState, id } = row?.getRowData();
      if (recordState === "审核中") {
        this.$callApi(
          `canWithdrawMallFramework&id=${id}`,
          {},
          (result) => {
            if (result?.data === true) {
              message = "您确定需要将选择的采购申请表撤回？";
              this.fetchApi(id, message);
            } else {
              this.$message.error(
                "撤回失败！审核人员已对您选择的采购申请表操作审核，不允许撤回"
              );
            }
            return true;
          }
        );
        return;
      }
      if (recordState === "未送审") {
        this.$message.warning(
          "撤回失败！您选择的采购申请表尚未送审，无需撤回"
        );
        return;
      }
      if (["审核通过"].includes(recordState)) {
        this.$message.warning(
          "撤回失败！您选择的采购申请表已完成审核，不可以撤回"
        );
        return;
      }
      if (["审核不通过"].includes(recordState)) {
        this.$message.warning(
          "撤回失败！您选择的采购申请表已完成审核，并且审核不通过，无需撤回。可以按照审核人员提出的审核意见完成修改后重新送"
        );
        return;
      }
      message = "确定撤回选中的项目?";
      this.fetchApi(id, message);
    },
    fetchApi(id, message) {
      let params = {
        ids: id,
        dataType: "MallFrameworkEntity",
        apiKey: "WFWITHDRAW",
      };
      this.$confirm(message, "提示", {
        confirmButtonText: "是",
        cancelButtonText: "否",
        type: "warning",
      }).then(() => {
        this.$callApiParams("WFWITHDRAW", params, (result) => {
          if (result.success) {
            this.$message.success("撤回成功！");
            this.init();
          }
          return true;
        });
      });
    },
    /**
     * 删除
     */
    remove(row) {
      const rows = row.params.rows;
      const { recordState, id } = row?.getRowData();
      if (recordState === "审核中") {
        this.$message.error("删除失败！您选择的采购申请表已提交审核，不允许删除。");
        return;
      }
      if (recordState === "审核通过") {
        this.$message.error("删除失败！您选择的采购申请表已审核通过，不允许删除");
        return;
      }
      this.$confirm(`您确定需要删除所选择的采购申请表吗？`, "提示", {
        confirmButtonText: "是",
        cancelButtonText: "否",
        type: "warning",
      })
        .then(() => {
          let ids = rows.reduce((str, obj) => {
            return str + obj.bizid + ",";
          }, "");
          ids = ids.slice(0, -1);
          this.$callApiParams(
            "deletePurProjectInfo",
            {
              bizids: ids,
            },
            (result) => {
              this.$message.success("删除成功！");
              this.init();
            }
          );
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.wrapper {
  overflow: hidden;
}
</style>
