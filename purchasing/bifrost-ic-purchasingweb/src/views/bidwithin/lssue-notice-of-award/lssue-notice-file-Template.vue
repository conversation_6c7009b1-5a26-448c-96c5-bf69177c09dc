<template>
  <div>
    <el-dialog
      ref="fileDialog"
      :title="titleText"
      width="850px"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="templateVisible"
    >
      <span>请选择中标通知书模板：</span>
      <el-table ref="elTable" :data="tableData" highlight-current-row border height="300" style="width: 100%; margin-top:10px;" @row-click="rowselect" @selection-change="handleSelectionChange">
        <template slot="empty"><el-empty description="暂无数据" :image-size="100"></el-empty></template>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" width="55"/>
        <el-table-column prop="name" label="招标公告名称" align="center" />
        <el-table-column prop="remark" label="说明" header-align="center" align="left" />
      </el-table>
      <div style="text-align: right; margin-top: 14px">
        <el-button class="btn-normal" size="medium" type="primary" @click="selectFile"
          >确定</el-button
        >
        <el-button
          class="btn-normal"
          size="medium"
          @click="templateVisible = false"
          >关闭</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { downloadFile } from "@/api/file/file"
export default {
  //编制采购文件
  name: "notice-file-Template",
  data() {
    return {
      templateVisible: false,
      titleText: "采购文件模板下载",
      tableData:[],
      selection:[]
    };
  },

  mounted() {},

  methods: {
    show(row) {
      this.templateVisible = true;
      this.$nextTick(() => {
        this.getInit();
      });
    },

    getInit() {
        this.$callApiParams('queryNoticeTemplate', {
          templateType:"中标通知书模板"
        },
        result => {
            if (result.success) {
                this.tableData = result.data
            }
            return true
        },result=>{return true})
    },

    handleSelectionChange(val){
        this.selection = val
    },

    rowselect(row){
        this.$refs.elTable.toggleRowSelection(row);
    },

    selectFile() {
        console.log(this.selection)
        if(this.selection.length==0){
            this.$message.error('请勾选要下载的文件');
            return
        }
        if(this.selection.length>1){
            this.$message.error('只能勾选一条数据');
            return
        }
        if(this.selection.length==1){
            console.log(this.selection)
            let row = this.selection[0]
            this.$callApiParams('queryNoticeTemplateContent', {
              ids:row.id
            },
            result => {
                if (result.success) {
                  this.$emit("noticeContent",result.data)
                  this.templateVisible = false
                }
                return true
            })
        }
    }


  },
};
</script>

<style lang="scss" scoped></style>
