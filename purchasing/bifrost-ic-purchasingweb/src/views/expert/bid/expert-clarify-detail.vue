<template>
  <el-dialog title="谈判报价" :visible.sync="editVisible" append-to-body width="90%"
    :close-on-click-modal='false'  class="dialog-no-top-padding" @close="onClose">
    <div class="nego-dialog">
      <div class="nego-files">
        <iframe :src="bidPdfPath"></iframe>
        <iframe :src="tenderPath"></iframe>
      </div>
      <div class="nego-form">
        <div class="bid-select">
          <el-select v-model="supplierId" @change="supplierChange">
            <el-option
              v-for="item in rows"
              :key="item.supplierInfoBizid"
              :label="item.supplierName"
              :value="item.supplierInfoBizid">
            </el-option>
          </el-select>
          <el-select v-model="attId" @change="change">
            <el-option
              v-for="item in tenderFiles"
              :key="item.attId"
              :label="item.attName"
              :value="item.attId">
            </el-option>
          </el-select>
        </div>
        <div class="nego-form-data">
          <div style="margin: 15px;">
            <div style="margin-bottom: 5px;">向【{{ form.row.supplierName }}】发起澄清说明</div>
            <el-input type="textarea" :rows="25" v-model="form.row.content"></el-input>
          </div>
          <div class="nego-buttons">
            <el-button type="primary" @click="onSave">保存并请供应商回复</el-button>
            <el-button @click="close">关闭</el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { downloadFile } from "@/api/file/file"

export default {
  props: { bid: Object },
  name: 'expert-clarify-detail',
  data() {
    return {
      projectId: undefined,
      bidPdfPath: undefined,
      tenderPath: undefined,
      tenderFiles: [],
      attList: [],
      registerList: [],
      attId: undefined,
      editVisible: false,
      form: { row: {}, },
      supplierId: undefined,
      rows: [],
    }
  },
  methods: {
    init() {
      const rows = JSON.parse(JSON.stringify(this.bid.registerList))
      this.form.row = rows[0]
      this.projectId = this.bid.projectInfoBizid
      this.supplierId = this.form.row.supplierInfoBizid
      this.rows = rows
      this.attList = this.bid.attList
      this.registerList = this.bid.registerList
      this.editVisible = true
      this.initRows()
    },
    getRegisterId() {
      const register = this.registerList.find(i => i.supplierInfoBizid == this.supplierId)
      if(register) {
        return register.bizid
      }
    },
    initRows() {
      const att = this.attList.find(att => att.appendixType == "采购文件pdf-系统生成")
      if(att) {
        if(att.url) {
          this.bidPdfPath = att.url
        } else {
          downloadFile(att.attId).then(res => {
              const blob = new Blob([res.data], { type: 'application/pdf' })
              this.bidPdfPath = URL.createObjectURL(blob)
              att.url = this.bidPdfPath
          })
        }
      }
      this.tenderFiles = this.attList.filter(
        att => att.fkGuid == this.getRegisterId()
         && /pdf$/.test(att.attName)
         && att.appendixType != "资格预审附件"
      )
      if(this.$isNotEmpty(this.tenderFiles)) {
        const tender = this.tenderFiles[0]
        this.attId = tender.attId
        if(tender.url) {
          this.tenderPath = tender.url
        } else {
          downloadFile(tender.attId).then(res => {
              const blob = new Blob([res.data], { type: 'application/pdf' })
              this.tenderPath = URL.createObjectURL(blob)
              tender.url = this.tenderPath
          })
        }
      }
    },
    close() {
      this.editVisible = false
    },
    supplierChange(supplierId) {
      this.supplierId = supplierId
      const row = this.rows.find(i => i.supplierInfoBizid == this.supplierId)
      this.form.row = row || {}
      this.initRows()
    },
    change(attId) {
      this.attId = attId
      const att = this.attList.find(i => i.attId == attId)
      if(att.url) {
        this.tenderPath = att.url
      } else {
        downloadFile(att.attId).then(res => {
            const blob = new Blob([res.data], { type: 'application/pdf' })
            this.tenderPath = URL.createObjectURL(blob)
            att.url = this.tenderPath
        })
      }
    },
    onSave() {
      this.$callApiParams("saveClarify", {
        projectId: this.projectId,
        supplierId: this.form.row.supplierInfoBizid,
        content: this.form.row.content
      })
    },
    onClose() {
      this.$emit("close")
    },
  }
}
</script>
