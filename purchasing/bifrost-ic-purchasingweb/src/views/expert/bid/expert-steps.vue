<template>
  <div class="bid-steps-container">
    <div class="bid-steps-tabs">
      <div
        v-for="(tabname, index) in tabs"
        :class="tabclass(index)"
        @click="onclick(index)"
        :key="index"
        v-if="tabname != 'hidden'"
      >
        {{ tabname }}
      </div>
    </div>
    <div class="bid-steps-content">
      <div>
        <component :is="currentComponent" :bid="root.bid" role="专家" />
      </div>
    </div>
    <div>
      <div style="text-align:center;">
        <template v-if="active == 0" >
          <template v-if="root.bid.signStatus != '是'">
            <el-checkbox v-model="isRead"
              label="我已阅读并遵守上述承诺" style="padding: 7px;">
            </el-checkbox>
          </template>
        </template>
      </div>
      <div class="bid-steps-button">
        <template v-if="active == 0" >
          <template v-if="root.bid.signStatus != '是'">
            <el-button type="primary" @click="signin">确认</el-button>
          </template>
          <el-button v-else @click="next" type="primary">下一步</el-button>
        </template>
        <template v-else>
          <el-button type="primary" @click="pre">上一步</el-button>
          <el-button v-if="active == 3" type="primary" @click="submitExamResult">提交符合性审查结果</el-button>
          <template v-if="active == 6">
            <el-button type="primary" @click="submitScoreList" v-if="showScoreButton">
              提交评分结果
            </el-button>
            <el-button type="primary" @click="next" v-if="bid.isLeader == '是'">定标</el-button>
          </template>
          <template v-if=" bid.reviewMethod == '综合评分+票选'">
            <el-button v-if="active == 7" type="primary" @click="submitBidExpertVoteResult">
              提交推荐结果
            </el-button>
            <el-button v-if="active == 8 && bid.isLeader == '是'" type="primary" @click="saveBidResult">
              确定定标
            </el-button>
          </template>
          <template v-else-if="bid.isEvaluationSeparation == '是'">
            <el-button v-if="active == 7" type="primary" @click="recommendSuppliersForPurchaser">
              推荐给采购人定标
            </el-button>
          </template>
          <template v-else>
            <el-button v-if="active == 7 && bid.isLeader == '是'" type="primary" @click="saveBidResult">确定定标</el-button>
          </template>
          <el-button v-if="tabs.length - 1 != active" type="primary" @click="next">下一步</el-button>
          <el-button v-if="active == 2" @click="clarify">澄清说明</el-button>
          <el-button v-if="[4, 7].includes(active) && bid.isLeader == '是'" @click="failBid">流标</el-button>
        </template>
        <el-button @click="back">返回项目列表</el-button>
      </div>
    </div>
    <expert-clarify :bid="bid" ref="clarify"/>
  </div>
</template>

<script>
import expertStep1 from './expert-step1.vue'
import expertStep2 from './expert-step2.vue'
import expertStep3 from './expert-step3.vue'
import expertStep4 from './expert-step4.vue'
import expertStep5 from './expert-step5.vue'
import expertStep6 from './expert-step6.vue'
import expertStep7 from './expert-step7.vue'
import expertStep8 from './expert-step8.vue'

import expertClarify from './expert-clarify.vue'
import expertStepSingle from './expert-step7-single.vue'
import expertStep8Nego from './expert-step8-nego.vue'
import expertStep8Price from './expert-step8-price.vue'
import expertStep8Vote from './expert-step8-vote.vue'
import expertStep9Vote from './expert-step9-vote.vue'

import expertStep8Tri from '../../host-new/host-step13-tri.vue'

import { downloadFile } from "@/api/file/file"

export default {
  props: { root: Object },
  name: 'exportSteps',
  components: {
    expertStep1, expertStep2, expertStep3, expertStep4, expertStep5,
    expertStep6, expertStep7, expertStep8,
    expertClarify, expertStepSingle, expertStep8Nego, expertStep8Tri,
    expertStep8Price, expertStep8Vote, expertStep9Vote
  },
  data() {
    return {
      tabs: [
        "签到",
        "推选评审组长",
        "开标一览表",
        "符合性审查",
        "符合性审查结果",
        "价格优惠扣减",
        "综合评分法评审",
        "定标",
      ],
      active: 0,
      step: 0,
      isRead: false,
    }
  },
  created() {
    this.$socket.addEventListener('message', (event) => {
      const data = JSON.parse(event.data)
      if(data.cmd == "LoadFile") {
        this.loadFile()
      }
    })
  },
  computed: {
    bid() {
      return this.root.bid
    },
    showScoreButton() {
      return !this.bid.isNeedNegotiate == '是' &&
        this.bid.reviewMethod != '最低价格法'
    },
    currentComponent() {
      let components= [
        expertStep1, expertStep2, expertStep3, expertStep4, expertStep5,
        expertStep6, expertStep7, expertStep8
      ]
      if(this.bid.isNeedNegotiate == '是') {
        if(this.active == 6) return expertStepSingle
        if(this.active == 7) return expertStep8Nego
      }
      if(this.bid.reviewMethod == '综合评分+票选') {
        if(this.active == 7) return expertStep8Vote
        if(this.active == 8) return expertStep9Vote
      }
      if(this.active == 7) {
        if(this.bid.procurementMethodName == '三方比价') {
          return expertStep8Tri
        }
        if(this.bid.reviewMethod == '最低价格法') {
          return expertStep8Price
        }
      }
      return components[this.active]
    },
  },
  mounted() {
    if(this.bid.procurementMethodName == '三方比价') {
      this.tabs[3] = "hidden"
      this.tabs[4] = "hidden"
      this.tabs[5] = "hidden"
      this.tabs[6] = "hidden"
    }
    if(this.bid.reviewMethod == '最低价格法') {
      this.tabs[6] = "hidden"
    }
    if(this.bid.reviewMethod == '综合评分+票选') {
      this.tabs.splice(7, 0, "推荐中标供应商")
    }
    this.$callApiParams('listBidAttachment', {
      projectId: this.bid.projectInfoBizid
    }, res => {
      this.bid.attList = res.data
      this.loadFile()
      return true
    })
    this.$callApiParams('listBidRegister', {
      projectId: this.bid.projectInfoBizid
    }, res => {
      this.bid.registerList = res.data
      return true
    })
    if(this.bid.isNeedNegotiate == '是') {
      this.$set(this.tabs, 6, this.bid.procurementMethodName)
    }
    this.refreshProcess(process => {
      this.active = process
    })
  },
  methods: {
    loadFile() {
      this.bid.attList.forEach(att => {
        if(!att.url) {
          // downloadFile(att.attId).then(res => {
          //     const blob = new Blob([res.data], { type: 'application/pdf' })
          //     att.url = URL.createObjectURL(blob)
          // })
        }
      })
    },
    refreshProcess(callback = () => {}) {
      this.$callApiParams("biddingProcess", {
        borId: this.bid.borId,
        role: "专家"
      }, res => {
        let process = res.data
        while(this.tabs[process] == "hidden" && process > 0) {
          process--
        }
        this.step = process
        callback(process)
        return true
      })
    },
    back() {
      this.root.visible = false
    },
    onclick(index) {
      if(this.step >= index) {
        this.active = index
      }
    },
    tabclass(index) {
      if(index == this.active) {
        return "active"
      }
      if(index <= this.step) {
        return "finish"
      }
    },
    pre() {
      this.active = Math.max(0, this.active - 1)
      if(this.tabs[this.active] == 'hidden') {
        this.pre()
      }
    },
    next() {
      this.refreshProcess(process => {
        let active = Math.min(this.tabs.length - 1, this.active + 1)
        while(this.tabs[active] == "hidden" && active < this.tabs.length) {
          active++
        }
        let step = Math.max(active, this.step)
        if(step <= process) {
          this.active = active
          this.step = step
          if(this.tabs[this.active] == 'hidden') {
            this.next()
          }
        } else if(this.tabs[this.active] == "综合评分法评审") {
          this.$message.warning("还有专家未提交评分结果, 请等待其他专家提交评分结果")
        } else if(this.active == 6 && this.bid.isNeedNegotiate == "是") {
          this.$message.warning("还有专家或供应商未确认谈判结果, 请等待其他专家或供应商确认")
        }
      })
    },
    signin() {
      if(!this.isRead) {
        return this.$message.warning("请先勾选承诺框")
      }
      this.$callApiParams("expertSignIn", {
        borId: this.bid.borId,
        projectId: this.bid.projectInfoBizid,
      }, res => {
        if(!this.bid.signId && res.data) {
          this.bid.signId = res.data
        }
        this.next()
        return true
      })
    },
    clarify() {
      this.$refs.clarify.open()
    },
    submitExamResult() {
      if(this.bid.examList) {
        this.$callApi("submitExamResult", this.bid.examList, res => {
          this.$message.success("提交成功")
          return true
        })
      } else {
        this.$message.warning("还有未评审的审查项")
      }
    },
    checkScoreList(scoreList) {
      if(this.$isEmpty(scoreList)) {
        this.$message.warning("还有未打分的审查项")
        throw -1
      }
      scoreList.forEach(i => {
        if(!i.itemScore) {
          this.$message.warning("还有未打分的审查项")
          throw -1
        }
      })
    },
    submitScoreList() {
      const scoreList = this.bid.scoreList
      this.checkScoreList(scoreList)
      this.$callApi("submitExpertScoreList", scoreList)
    },
    saveBidResult() {
      this.$callApiParams("saveBidResult", {
        borId: this.bid.borId,
        projectId: this.bid.projectInfoBizid,
        supplierId: [this.bid.bidResult.supplierId].join(","),
        reason: this.bid.bidResult.reason
      })
    },
    submitBidExpertVoteResult() {
      this.$callApiParams("submitBidExpertVoteResult", {
        borId: this.bid.borId,
        projectId: this.bid.projectInfoBizid,
        supplierId: this.bid.voteResult.supplierId,
        reason: this.bid.voteResult.reason
      })
    },
    recommendSuppliersForPurchaser() {
      if(!this.bid.bidResult.supplierId) {
        return this.$message.warning("请选择中标供应商")
      }
      this.$callApiParams("recommendSuppliersForPurchaser", {
        borId: this.bid.borId,
        projectId: this.bid.projectInfoBizid,
        supplierId: this.bid.bidResult.supplierId.join(","),
        reason: this.bid.bidResult.reason
      })
    },
    failBid() {
      this.$prompt('流标', '流标原因', {
        distinguishCancelAndClose: true,
        customClass: 'custom-prompt',
        inputPattern: /\S/,
        inputErrorMessage: '输入不能为空',
        showCancelButton: true,
        inputType: 'textarea',
        closeOnClickModal: false,
        inputPlaceholder: '请输入流标原因'
      }).then(({ value }) => {
        this.$callApiParams('updateBidStatus'
        , {
          borId: this.bid.borId,
          status: "流标",
          failureCause: value
        }, res => {
          this.back()
        })
      })
    },
  }
}
</script>
