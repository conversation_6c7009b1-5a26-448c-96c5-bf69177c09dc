<template>
  <dialogTem
    title="添加供应商名称"
    ref="dialogTemRef"
    width="50%"
    height="450px"
    showFooter
    @handleClose="closeDialog"
  >
    <div slot="content">
      <div style="padding: 10px 50px 0 10px">
        <el-form
          ref="editForm"
          label-width="160px"
          :model="projectInfoForm"
          :rules="rules"
          :disabled="isDisabled"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="供应商名称" prop="supplierInfoName">
                <el-input
                  clearable
                  show-word-limit
                  v-model="projectInfoForm.supplierInfoName"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="统一社会信用代码" prop="uscc">
                <el-input
                  clearable
                  show-word-limit
                   maxlength="18"
                   minlength="18"
                  v-model="projectInfoForm.uscc"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="投标文件" prop="attachment" >
                <el-upload
                  ref="upload"
                  action=""
                  :file-list="fileList"
                  :on-change="handleChange"
                  :show-file-list="true"
                  :auto-upload="false"
                  :limit="1"
                  :on-exceed="handleExceed"
                  :on-remove="handleRemove"
                  accept=".doc,.docx,.pdf"
                >
                  <el-button slot="trigger" size="mini">选择文件</el-button>
                  <div slot="tip" class="el-upload__tip">
                    只能上传doc/docx/pdf文件，且不超过300MB
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="投标报价（元）" prop="quotedPrice">
                <el-input
                  clearable
                  show-word-limit
                  v-model="projectInfoForm.quotedPrice"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="是否中标供应商" prop="isBid">
              <el-radio v-model="projectInfoForm.isBid" label="是">是</el-radio>
              <el-radio v-model="projectInfoForm.isBid" label="否">否</el-radio>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="projectInfoForm.isBid=='是'">
              <el-form-item label="中标金额（元）" :prop="projectInfoForm.isBid=='是'  ? 'bidMoney' : ''">
                <el-input
                  clearable
                  show-word-limit
                  v-model="projectInfoForm.bidMoney"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div slot="footer">
      <div style="text-align: center; margin-top: 20px">
        <el-button type="primary" v-if="!isDisabled" @click="handleSaveClick" size="medium" :loading="sLoading">确定</el-button>
        <el-button size="medium" @click="closeDialog">关闭</el-button>
      </div>
    </div>
  </dialogTem>
</template>

<script>
import dialogTem from "@/components/dialogTem/index.vue";
export default {
  name: "inSupplier",
  components: {
    dialogTem,
  },
  data() {
    return {
      rules: {
        supplierInfoName: [
          { required: true, message: "请输入供应商名称", trigger: "blur" },
          {
            min: 3,
            max: 20,
            message: "长度在 3 到 20 个字符",
            trigger: "blur",
          },
        ],
        uscc: [
          {
            required: true,
            message: "请输入统一社会信用代码",
            trigger: "blur",
          },
          // 添加其他校验规则
        ],
        quotedPrice: [
          { required: true, message: "请输入投标报价（元）", trigger: "blur" },
          // 添加其他校验规则
        ],
        attachment: [
          { required: true, message: "请上传投标文件", trigger: "change" },
          {
            validator: (rule, value, callback) => {
              // 判断对象是否为空
              if (Object.keys(this.projectInfoForm.attachment).length === 0) {
                console.log(this.projectInfoForm.attachment);
                callback(new Error("请上传投标文件"));
              } else {
                callback();
              }
            },
          },
        ],
        isBid: [
          {
            required: true,
            trigger: "change",
            message: "请选择是否投标供应商",
          }
        ],
        bidMoney: [
          {
            required: true,
            trigger: "blur",
            message: "请输入中标金额",
          },
        ]
      },
      fileList: [],
      attList: [],
      projectInfoForm: {
        isEnable: "否",
        projectInfoBizid: "",
        supplierInfoName: "",
        uscc: "",
        quotedPrice: "",
        attachment: {},
        isBid: "是",
        bidMoney: ""
      },
      sLoading: false,
      saveTimeoutId: null, // 用于存储setTimeout的ID
      isDisabled: false
    };
  },
  methods: {
    openDialog(params, type) {
      this.isDisabled = type ? true : false
      this.$refs.dialogTemRef.openDialog();
      if(type) {
       this.projectInfoForm = params
       this.fileList =  [{name: params.attName, attId: params.attId}]
      }else {
        this.fileList = []
        this.projectInfoForm = {
        projectInfoBizid: "",
        supplierInfoName: "",
        uscc: "",
        quotedPrice: "",
        attachment: {},
        isBid: "是",
        bidMoney: ""
      };
      }
      this.sLoading = false
      this.projectInfoForm.projectInfoBizid = params;
    },
    closeDialog() {
      this.$refs.dialogTemRef.closeDialog();
    },

    handleChange(file, fileList, scope) {
      // 文件是否已存在上传列表中
      const isExists = this.fileList.some((f) => f.name === file.name);
      if (isExists) {
        this.$message.error("文件：" + file.name + "已存在，请勿重复上传!");
        // 文件展示列表是将新添加的文件放在数组末尾
        fileList.pop();
        return;
      }
      const isGt100M = Number(file.size / 1024 / 1024) > 100;
      if (isGt100M) {
        const currIdx = fileList.indexOf(file);
        fileList.splice(currIdx, 1);
        this.$message.error("文件大小不能超过100MB，请压缩后重新上传！");
        return;
      }
      if (file) {
        // this.filedForm.attName = file.name
        this.handlefiledSumbit(file);
        this.fileList.push(file);
      }
    },
    handlefiledSumbit(item) {
      const formData = new FormData();
      formData.append("files", item.raw, item.name);
      if (formData.has("files")) {
        this.$callApi("uploadAttachment", formData, (result) => {
          if (result.success) {
            if (this.$isNotEmpty(result.data)) {
              this.projectInfoForm.attachment = result.data.attList[0];
            }
          }
        });
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，当前已有 ${fileList.length} 个文件`
      );
    },
    handleRemove(file, fileList) {
      const currIdx = this.fileList.indexOf(file);
      this.fileList.splice(currIdx, 1);
    },
    handleSaveClick() {
      // 如果有正在进行的保存操作（即setTimeout还未执行），则清空并重新开始计时
      if (this.saveTimeoutId) {
        clearTimeout(this.saveTimeoutId);
      }

      // 设置一个新的setTimeout，如果在规定时间内没有新的点击事件，则执行save方法
      this.saveTimeoutId = setTimeout(() => {
        this.save();
      }, 1000); // 1000毫秒内连续点击，只会执行最后一次点击后的操作
    },
    save(showErrorMessage = true) {
      this.$refs.editForm.validate(
        (valid) => {
          if (valid) {
            this.$callApi(
              "saveGovBiddingSupplier",
              { ...this.projectInfoForm },
              (result) => {
                if (result.code === '9999') {
                    // 成功处理逻辑
                    this.sLoading = false
                    this.closeDialog()
                    this.$emit('successSup')
                }else {
                    this.sLoading = false
                }
              },
              (error) => {
                this.sLoading = false
              },
              {isSave:false},
              showErrorMessage
            );
          } else {
            this.$message.error("请填写必填项!");
            this.sLoading = false
            return false;
          }
        },
        (error) => {

        }
      );
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
</style>
