<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: 作者姓名
 * @Date: 创建日期
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-09-04 16:46:32
-->
<template>
  <div>
    <el-dialog
      :title="homeTitle"
      :visible.sync="dialogVisible"
      width="70%"
      :before-close="handleClose"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      v-if="dialogVisible"
    >
      <div style="height: calc(80vh - 0px) !important;">
        <!-- 申请表信息 -->
        <PurchaseCardTable
          ref="expenseCardRef"
          topTitle="项目信息"
          class="mb-2"
          :titleStyle="{ color: '#006CFF' }"
        >
          <el-form :model="form" label-width="230px" ref="form" >
            <!-- 申请表名称（占一整行） -->
            <el-row>
              <el-col :span="12">
                <el-form-item label="采购项目名称" prop="projectName">
                  <el-input
                    v-model="form.projectName"
                    placeholder="请输入采购项目名称"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="采购项目编号">
                  <el-input
                    v-model="form.recordNumber"
                    disabled
                    placeholder="系统生成"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="采购组织形式" prop="procurementCategory">
                  <el-input
                    v-model="form.procurementCategory"
                    placeholder="请输入采购组织形式"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="采购所属部门" prop="deptName">
                  <el-input
                    v-model="form.deptName"
                    placeholder="请输入采购所属部门"
                    @click.native="refDataDept"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="代理（或执行）机构"
                  prop="purchaseActuator"
                >
                  <el-input
                    v-model="form.purchaseActuator"
                    disabled
                    placeholder="无"
                  />
                </el-form-item>
              </el-col>
              <!-- 采购项目经办人 -->
              <el-col :span="12">
                <el-form-item label="采购项目经办人" prop="projectManagerName">
                  <el-input
                    v-model="form.projectManagerName"
                    placeholder="请输入采购项目经办人"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <!-- 采购方式（原下拉框） -->
              <el-col :span="12">
                <el-form-item label="采购方式">
                  <el-input
                    v-model="form.procurementMethodName"
                    disabled
                    placeholder="无"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="本次采购使用的经费卡总额（元）"
                  prop="budgetAmount"
                >
                  <el-input
                    v-model="form.budgetAmount"
                    disabled
                    placeholder="自动计算金额"
                    style="background: #f5f7fa"
                  />
                </el-form-item>
              </el-col>
              <!-- 采购标的类别（下拉框） -->
              <el-col :span="12">
                <el-form-item
                  label="采购标的类别"
                  prop="purchaseClassification"
                >
                  <el-input
                    v-model="form.purchaseClassification"
                    disabled
                    placeholder="无"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="form.selfPurchaseDescription">
                <el-form-item
                  :label="`${form.selfPurchaseType}项目细分选项：`"
                  prop="selfPurchaseDescription"
                >
                  <el-input
                    v-model="form.selfPurchaseDescription"
                    disabled
                    placeholder="无"
                  >
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </PurchaseCardTable>
        <!--  -->
        <PurchaseCardTable
          ref="expenseCardRef"
          topTitle="项目使用的经费卡信息"
          class="mb-2"
          :titleStyle="{ color: '#006CFF' }"
        >
          <el-table
            :data="expenseCardList"
            border
            stripe
            align="center"
            header-align="center"
            height="150"
            style="width: 100%"
            :header-cell-style="{ background: '#f5f7fa', fontWeight: 'bold' }"
          >
            <!-- 动态生成表格列 -->
            <el-table-column
              v-for="column in exTableColumns"
              :key="column.prop"
              :prop="column.prop"
              :width="column.width"
              :label="column.label"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template slot-scope="scope">
                <!-- 金额字段显示格式化后的值 -->
                <span
                  v-if="
                    [
                      'postavailableAmount',
                      'expenseCardLimit',
                      'availableAmount',
                      'usedbudgetAmount',
                      'postAvailableAmount',
                      'thisUsedAmount',
                      'usedAmount',
                    ].includes(column.prop)
                  "
                >
                  {{ $formatMoney(scope.row[column.prop]) }}
                </span>
                <!-- 普通字段直接显示值 -->
                <span v-else>{{ scope.row[column.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </PurchaseCardTable>
        <PurchaseCardTable
          topTitle="竞价信息"
          class="mb-2"
          :titleStyle="{ color: '#006CFF' }"
        >
          <el-form
            ref="biddingForm"
            :model="biddingInfo"
            label-width="120px"
            class="mb-4"
            :show-message="false"
          >
            <el-row :gutter="20">
              <el-col :span="10">
                <el-form-item label="设备类别" prop="equipmentCategory">
                  <el-input
                    v-model="biddingInfo.equipmentCategory"
                    placeholder="请输入设备类别"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 经费科目 -->
              <el-col :span="10">
                <el-form-item label="经费科目" prop="fundingSubjects">
                  <el-input
                    v-model="biddingInfo.fundingSubjects"
                    placeholder="请输入经费科目"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 报价要求 -->
              <el-col :span="10">
                <el-form-item label="报价要求" prop="requirement">
                  <el-input
                    v-model="biddingInfo.requirement"
                    placeholder="请输入报价要求"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 币种 -->
              <el-col :span="10">
                <el-form-item label="币种" prop="currencyType">
                  <el-input
                    v-model="biddingInfo.currencyType"
                    placeholder="请输入币种"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 发票类型 -->
              <el-col :span="10">
                <el-form-item label="发票类型" prop="invoiceType">
                  <el-input
                    v-model="biddingInfo.invoiceType"
                    placeholder="请输入发票类型"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24" class="red-border-field">
                <el-form-item label="竞价网申购单号" prop="orderNumber">
                  <el-input
                    v-model="biddingInfo.orderNumber"
                    placeholder="请输入竞价网申购单号"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </PurchaseCardTable>
        <PurchaseCardTable
          ref="expenseCardRef"
          topTitle="竞价设备信息"
          class="mb-2"
          :titleStyle="{ color: '#006CFF' }"
        >
          <el-form ref="deviceForm" :model="deviceInfo" label-width="120px">
            <el-row :gutter="20">
              <!-- 设备名称 -->
              <el-col :span="10">
                <el-form-item label="设备名称" prop="deviceName">
                  <el-input
                    v-model="deviceInfo.deviceName"
                    placeholder="请输入设备名称"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 规格配置 -->
              <el-col :span="10">
                <el-form-item label="规格配置" prop="specification">
                  <el-input
                    v-model="deviceInfo.specification"
                    placeholder="请输入规格配置"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 设备品牌 -->
              <el-col :span="10">
                <el-form-item label="设备品牌" prop="brand">
                  <el-input
                    v-model="deviceInfo.brand"
                    placeholder="请输入设备品牌"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 申购数量 -->
              <el-col :span="10">
                <el-form-item label="申购数量" prop="quantity">
                  <el-input
                    v-model="deviceInfo.quantity"
                    placeholder="请输入申购数量"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 设备型号 -->
              <el-col :span="10">
                <el-form-item label="设备型号" prop="model">
                  <el-input
                    v-model="deviceInfo.model"
                    placeholder="请输入设备型号"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 单位度量 -->
              <el-col :span="10">
                <el-form-item label="单位度量" prop="unitMeasure">
                  <el-input
                    v-model="deviceInfo.unitMeasure"
                    placeholder="请输入单位度量"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 预算单价 -->
              <el-col :span="10">
                <el-form-item label="预算单价" prop="budgetUnitPrice">
                  <el-input
                    v-model="deviceInfo.budgetUnitPrice"
                    placeholder="请输入预算单价"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 附件 -->
              <el-col :span="10">
                <el-form-item label="附件" prop="attName">
                  <el-input
                    v-model="deviceInfo.attName"
                    placeholder="请输入附件名称"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </PurchaseCardTable>
      </div>
      <div slot="footer" class="dialog-footer text-center">
        <el-button type="primary" @click="handleSure">确定</el-button>
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import PurchaseCardTable from "../../pur-project-manage/pur-project-apply/components/purchaseCardTable.vue";
export default {
  name: "applyNumberDialog",
  components: { PurchaseCardTable },
  props: {},
  data() {
    return {
      dialogVisible: false, // 控制对话框的显示与隐藏
      homeTitle: "登记竞价网申购单号", // 对话框标题
      form: {
        projectName: "",
        recordNumber: "",
        procurementCategory: "",
        deptName: "",
        projectManagerName: "",
        budgetAmount: "",
        purchaseClassification: "",
        selfPurchaseDescription: "",
      },
      deviceInfo: {
        deviceName: "",
        specification: "",
        brand: "",
        quantity: "",
        model: "",
        unitMeasure: "",
        budgetUnitPrice: "",
        attName: "",
      },
      biddingInfo: {
        equipmentCategory: "",
        fundingSubjects: "",
        requirement: "",
        currencyType: "",
        invoiceType: "",
      },
      exTableColumns: [
        {
          prop: "expenseCardYear",
          label: "经费卡年度",
          width: "100",
        },
        {
          prop: "expenseCardNo",
          label: "经费卡编号",
          width: "150",
        },
        {
          prop: "expenseCardName",
          label: "经费卡名称",
          width: "150",
        },
        {
          prop: "expenseCardDeptName",
          label: "经费卡部门",
          width: "100",
        },
        {
          prop: "expenseCardPrincipal",
          label: "经费卡负责人",
          width: "100",
        },
        {
          prop: "expenseCardLimit",
          label: "经费卡限额（元）",
          width: "140",
          isMoney: true,
        },
        {
          prop: "availableAmount",
          label: "本次申报前剩余金额（元）",
          width: "140",
          isMoney: true,
        },
        {
          prop: "thisUsedAmount",
          label: "本次使用金额（元）",
          width: "160",
        },
        {
          prop: "postAvailableAmount",
          label: "本次使用后剩余金额（元）",
          width: "140",
          isMoney: true,
        },
        {
          prop: "expenseCardIndate",
          label: "经费有效期",
          width: "140",
        },
        {
          prop: "isProcurementPlan",
          label: "是否关联政府集采计划",
          isPlan: true,
        },
      ],
      expenseCardList: [],
      bizid: "",
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    
  },
  methods: {
    // 打开对话框
    openDialog(bizid) {
      this.bizid = bizid;
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.getAllData();
      })
    },
    // 关闭对话框
    handleClose() {
      this.dialogVisible = false;
    },
    // 确定按钮逻辑（可扩展）
    handleSure() {
      // 自定义确定操作逻辑
      this.handleResult();
    },
        // 保存订单号
    handleResult() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$callApiParams(
            "saveSelfOrderNumber",
            {
              ids: this.bizid,
              orderNumber: this.biddingInfo.orderNumber,
            },
            (result) => {
              if (result.success) {
                this.dialogVisible = false;
                this.$message({
                  message: result.msg,
                  type: "success",
                });
                this.$emit("init");
              } else {
                this.dialogVisible = true;
                this.$message({
                  message: "保存失败，请重试。",
                  type: "error",
                });
              }
              return true;
            }
          );
        } else {
          this.$message({
            message: "请填写必填项！",
            type: "warning",
          });
        }
      });
    },
    getAllData() {
      console.log('getAllData');
      this.$callApiParams('getPurProjectInfoConfirmVo', {
        bizid: this.bizid,
      }, ({ data }) => {
        this.form = data.purProjectInfoEntity; // 项目基本信息
        this.form.budgetAmount = this.$formatMoney(data.purProjectInfoEntity.budgetAmount); // 格式化预算金额
              // 新增：处理"工程类"字符串截取逻辑
        if (this.form.purchaseClassification?.includes('工程类')) {
          this.form.purchaseClassification = this.form.purchaseClassification.split('-')[0];
        }
        this.expenseCardList = data.expenseCardList // 经费卡信息
        this.tableData = data.productDetailEntityList
        this.$set(this.form, 'orderNumber', data.selfBiddingEntity.orderNumber);
        this.form.recordNumber = data.selfBiddingEntity.recordNumber;
        this.form.projectName = data.selfBiddingEntity.projectName;
        this.form.deptName = data.selfBiddingEntity.deptName;

        this.biddingInfo = data.selfBiddingEntity;
        this.deviceInfo = data.selfBiddingEntity;
        return true;
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.red-border-field ::v-deep .el-input__inner {
  border: 1px solid #f56c6c !important;
}
</style>