<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-08 17:41:06
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-30 14:02:24
-->
<template>
  <div style="padding: 0 20px 0 0">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="项目信息" name="first">
        <div class="headerStyle">
          <span class="icon-line"></span>
          <h4>项目信息</h4>
        </div>
        <el-form
          ref="infoForm"
          label-width="170px"
          :model="projectInfoForm"
          :rules="rules"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="采购项目名称">
                <el-input
                  clearable
                  v-model="projectInfoForm.projectInfoName"
                  :disabled="true"
                  placeholder=""
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 只有政府集采才能填 其他禁用 -->
              <el-form-item :label="bidText"   
                 prop="biddingNo"
                :rules="[
                 { required: true, message: `请输入${bidText}`, trigger: 'blur' },
                ]">
                <el-input
                  clearable
                  v-model="projectInfoForm.biddingNo"
                  :placeholder="`请输入${bidText}`"
                  :disabled="!isEdit || bidText === '单位自采招标项目编号'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="采购组织形式">
                <el-input
                  clearable
                  v-model="projectInfoForm.procurementCategory"
                  :disabled="true"
                  placeholder=""
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="采购方式">
                <el-input
                  clearable
                  v-model="projectInfoForm.procurementMethodName"
                  :disabled="true"
                  placeholder=""
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="招标执行机构">
                <el-input
                  clearable
                  v-model="projectInfoForm.purchaseActuator"
                  :disabled="true"
                  placeholder=""
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="isShowReviewMethod">
              <el-form-item
                label="评审方法"
                prop="reviewMethod"
                style="margin-bottom: 16px"
              >
                <el-select
                  v-model="projectInfoForm.reviewMethod"
                  :disabled="!isEdit"
                  @change="handleReviewMethod"
                  placeholder="请选择评审方法"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in reviewProcessOptions"
                    :key="item.tcode"
                    :label="item.tname"
                    :value="item.tname"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="isShowAgencyName">
              <el-form-item label="招标代理机构" prop="agencyName">
                <el-input
                  clearable
                  v-model="projectInfoForm.agencyName"
                  :disabled="!isEdit"
                  placeholder="请输入招标代理机构"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="isShowEvaluationSeparation">
              <el-form-item label="是否评定分离" prop="isEvaluationSeparation">
                <el-radio-group
                  v-model="projectInfoForm.isEvaluationSeparation"
                  :disabled="!isEdit"
                >
                  <el-radio label="是">是</el-radio>
                  <el-radio label="否">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报价类型" prop="quotationType">
                <el-select
                  v-model="projectInfoForm.quotationType"
                  :disabled="!isEdit"
                  placeholder="请选择报价类型"
                >
                  <el-option
                    v-for="item in quotationOptions"
                    :key="item.tcode"
                    :label="item.tname"
                    :value="item.tvalue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <!-- 是否有价格优惠扣除 单选 字段名 enable -->
           <el-col :span="12">
              <el-form-item label="是否有价格优惠扣除" prop="enable">
                <el-radio-group
                  v-model="projectInfoForm.enable"
                  :disabled="!isEdit"
                >
                  <el-radio label="是">是</el-radio>
                  <el-radio label="否">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
       <!-- 如果选择的是 是,则需要展示一个el-tbale  第一列是字段名 不能编辑 第二列是字段值 可以输入 -->
        <div class="headerStyle"  v-if="projectInfoForm.enable == '是'">
            <span class="icon-line"></span>
            <h4>价格优惠扣除比例</h4>
        </div>
        <el-table
          v-if="projectInfoForm.enable == '是'"
          :data="discountConfig"
          style="width: 100%; margin-bottom:10px;"
          border
          height="200px"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
        >
          <el-table-column prop="tname" label="项目类别" width="350">
          </el-table-column>
          <el-table-column prop="tvalue" label="价格优惠扣除比例 %">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.tvalue"
                placeholder="请输入百分比数值"
                @change.native="handleInput(scope.$index, scope.row.tvalue)"
                :class="{ 'input-error':!scope.row.tvalue && scope.row.tvalue!== 0 }"
                :disabled="!isEdit"
                :controls="false"
                :min="0"
              >
              </el-input-number>
              <span style="margin-left: 8px">%</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="attrFile" v-if="!showConfigModule">
          <div class="headerStyle">
            <span class="icon-line"></span>
            <h4>采购文件</h4>
          </div>
          <div id="base-attachment" style="height: 320px">
            <base-attachment ref="baseAttachment"></base-attachment>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="在线制作" name="second" v-if="showConfigModule">
          <configModuleVue
          ref="configModuleVue"
          :isCustomBtn="false"
          :projectInfoBizid="projectInfoBizid"
          :projectInfoStatus="projectInfoStatus"
          :isAudit="isAudit"
          style="height: calc(80vh - 156px);"
          module="make"
          :isDetail="TypeText.includes('详情')"
          :moduleType="TypeText"
          :projectStatus="isProjectInfoStatus"
          ></configModuleVue>
      </el-tab-pane>
      <el-tab-pane label="评审文件关系设置" name="third" v-if="isMakeFile">
        <createFile
        ref="createFileRef"
        :btnDisabled="TypeText.includes('详情') || TypeText.includes('审核')"
        ></createFile>
      </el-tab-pane>
    </el-tabs>
    <produce-file-Template ref="producefileRef" />
  </div>
</template>

<script>
import configModuleVue from '../../business/documentsSeting/components/configModule.vue';
import createFile from './components/createFile.vue';
import produceFileTemplate from "./produce-file-Template";
export default {
  //编制采购文件
  name: "produce-pur-fileInfo",
  components: { produceFileTemplate, configModuleVue, createFile },
  provide() {
    return {
      deleteData: true,
    };
  },
  computed: {
  },
  data() {
    return {
      isAudit: false,
      ids: '',
      showConfigModule: false,
      projectInfoBizid: '',
      projectInfoStatus: '',
      isProjectInfoStatus: '',
      projectStatus: "",
      activeName: 'first',
      purfileVisible: false,
      titleText: "编制采购文件",
      isMakeFile: false,
      TypeText: "",
      projectInfoForm: {
        projectInfoName: "",
        biddingNo: "",
        purchaseCategory: "",
        procurementMethodName: "",
        purchaseActuator: "",
        reviewMethod: "",
        agencyName: "",
        isEvaluationSeparation: "否",
        quotationType: "",
        isContractProcessedInSystem: "",
        enable: '是'
      },
      discountConfig: [
        { 
          tname: '小微企业扣除比例', 
          tvalue: 0,
          key: 'smallEnterpriseDeductionRate'
        },
        { 
          tname: '监狱企业扣除比例', 
          tvalue: 0,
          key: 'prisonEnterpriseDeductionRate'
        },
        { 
          tname: '残疾人福利性单位扣除比例', 
          tvalue: 0,
          key: 'disabilityWelfareUnitDeductionRate'
        }
      ],
      disBizid: "",
        // 新增一个对象用于保存最终格式的扣除比例
      finalDiscountConfig: {},
      isShowEvaluationSeparation: false,
      isShowAgencyName: false,
      isShowReviewMethod: false,
      isEdit: true,
      rules: {
        reviewMethod: [
          { required: true, message: "请选择评审方法", trigger: "change" },
        ],
        agencyName: [
          { required: true, message: "请输入招标代理机构", trigger: "blur" },
        ],
        isEvaluationSeparation: [
          { required: true, message: "请选择是否评定分离", trigger: "change" },
        ],
        quotationType: [
          { required: true, message: "请选择报价类型", trigger: "change" },
        ],
        isContractProcessedInSystem: [
          { required: true, message: "请选择是否在本系统编制、审核、备案合同", trigger: "change" },
        ],
        enable: [
          { required: true, message: "请选择是否有价格优惠扣除", trigger: "change" },
        ]
      },
      reviewProcessOptions: [],
      quotationOptions: [],
      attData: {
        main: {
          formType: "采购文件",
        },
        attList: [],
        extData: {
          currentUser: "",
          attTempId: "",
          initRefDataVoFromId: undefined,
          batchDownloadName: "",
          attTypeTableName: "ELE_CGWJFJLX_ATT_TYPE",
          bizDataId: "",
          appendixType: "采购文件",
        },
        data: {
          id: "",
        },
      },
      bidText: ""
    };
  },
  computed: {
  },
  created() {},
  mounted() {},
  methods: {
    handleClick(tab, event) {
        if(this.activeName ==='third') {
           this.$refs.createFileRef.init(this.ids)
           this.$emit('visibleBtn', false)
        } else if(this.activeName === 'second') {
           this.$refs.configModuleVue.init()
           this.$emit('visibleBtn', false)
        } else {
           this.$emit('visibleBtn', true)
        }
    },
    handleInput(index, value) {
      // 限制输入范围在 0 - 100 之间
      if (value < 0) {
        value = 0;
      } else if (value > 100) {
        value = 100;
      }
      this.discountConfig[index].tvalue = value;
      // 将输入值同步到 finalDiscountConfig 对应的 key 上
      const key = this.discountConfig[index].key;
      this.finalDiscountConfig[key] = value;
    },
    init(row, text, isEdit, showConfigModule, isMakeFile = false) {
      // ，当采购组织形式为“政府集采”时，显示“政府集采招标项目编号”字段，并且为必填项；当采购组织形式为“单位自采”时，显示“单位自采招标项目编号”字段。
      this.bidText = row?.getRowData()?.procurementCategory === '政府集采' ? '政府集采招标项目编号' : '单位自采招标项目编号'
      this.isMakeFile = isMakeFile && showConfigModule
      this.showConfigModule = showConfigModule ? true : false;
      this.$emit('visibleBtn', this.activeName === 'first' ? true : false )
      this.TypeText = text
      // this.getInlineProduct()
      this.isProjectInfoStatus = row.getRowData()?.projectInfoStatus
      this.activeName = "first"

      this.$emit('visibleBtn', true)

      this.isAudit = (text.includes('审核') || text.includes('详情')) ? true : false;
      this.ids = row.getRowIds();
      this.isEdit = isEdit;
      this.rowId = row.getRowData().ID;
      this.projectInfoBizid = row.getRowData()?.bizid;
      if(text === '审核') {
        this.projectInfoStatus = '是'
      } else {
        this.projectInfoStatus = this.isProjectInfoStatus === '审核中' || '审核不通过' ? '是' : '否';
      }
      this.getInfoMessage(row);
      if (this.showConfigModule) {
        // 20250610 初始化的时候，初始化第二个节点（在线制作）的内容
        this.$nextTick(() => {
          this.$refs.configModuleVue.init()
        })
        return false;
      }
      var buttons = [
        {
          text: "按采购文件模板生成",
          icon: "el-icon-edit",
          enabledType: "0",
          click: (row) => {
            this.$refs.producefileRef.show("采购文件");
          },
        },
      ];
      this.$nextTick(() => {
        this.attData.data.id = row.getRowData().purProjectInfoBizid || row.getRowData().ID;
        this.attData.extData.attTempId = row.getRowData().purProjectInfoBizid || row.getRowData().ID;
        //每一次点击给isNoDeleteBts初始化，只针对采购文件审核页
        this.$refs.baseAttachment.$children[0].isNoDeleteBts = false;
        this.$refs.baseAttachment.$children[0].isCform = false;
        //isEdit：true 查看
        if (isEdit) {
          this.$refs.baseAttachment.$children[0].btAddText = "上传采购文件";
          this.$refs.baseAttachment.$children[0].isNoAddDeleteBts = false;
          this.$refs.baseAttachment.$children[0].initMeta(
            this.attData,
            buttons
          );
        } else {
          //审核的时候传进来的是false
          this.$refs.baseAttachment.$children[0].btAddText = "上传采购文件";
          this.$refs.baseAttachment.$children[0].isNoAddDeleteBts = true;
          this.$refs.baseAttachment.$children[0].initMeta(this.attData);
          //this.$refs.baseAttachment.initCommonBiz(paramsEx.isEdit, baseObj.dataVo)
        }
        this.$refs.baseAttachment.$children[0].$refs.filedialog.acceptType =
          ".doc,.docx";
      });
    },

    getInfoMessage(row) {
      this.$callApiParams(
        "selectProjectInfoItem",
        { id: row.getRowData().ID },
        (result) => {
          this.clearData();
          this.projectInfoForm = result.data.projectInfoVo;
          this.projectInfoForm.isEvaluationSeparation = result.data
            .projectInfoVo.isEvaluationSeparation
            ? result.data.projectInfoVo.isEvaluationSeparation
            : "否";
          const enable = result.data
           .discountConfig?.enable
           ? result.data.discountConfig?.enable
            : "是";
          this.$set(this.projectInfoForm, 'enable', enable)
          // 处理 discountConfig 数据
          const apiData = result.data?.discountConfig || [];
          this.$set(this, 'disBizid', apiData.bizid)
          this.discountConfig = this.discountConfig.length > 0 && this.discountConfig.map(item => {
            const value = apiData[item.key];
            return {
              ...item,
              tvalue: value!== undefined? Number(value) : 0
            };
          });

          // 将数据同步到 finalDiscountConfig
          this.discountConfig.forEach(item => {
            this.finalDiscountConfig[item.key] = item.tvalue;
          });
          this.selectListOpt();
          if (result.data.projectInfoVo.purchaseActuator === "第三方代理机构") {
            this.isShowAgencyName = true;
          } else {
            this.isShowAgencyName = false;
          }
          if (result.data.projectInfoVo.reviewMethod === "综合评分法") {
            this.isShowEvaluationSeparation = true;
          } else {
            this.isShowEvaluationSeparation = false;
          }
          if (
            result.data.projectInfoVo.procurementMethodName === "公开招标" ||
            result.data.projectInfoVo.procurementMethodName === "邀请招标"
          ) {
            this.isShowReviewMethod = true;
          } else {
            this.isShowReviewMethod = false;
          }
          return true;
        }
      );
    },

    //选择评审方法
    handleReviewMethod(val) {
      this.isShowEvaluationSeparation = val === "综合评分法" ? true : false;
    },

    save(callBack) {
      this.$refs["infoForm"].validate((valid) => {
        if (valid) {
       // 当 projectInfoForm.enable 为 '是' 时，校验扣除比例
       if (this.projectInfoForm.enable === '是') {
            let hasEmptyValue = false;
            // 校验每个扣除比例是否为空
            for (const item of this.discountConfig) {
              if (!item.tvalue && item.tvalue!== 0) {
                hasEmptyValue = true;
                break;
              }
            }
            if (hasEmptyValue) {
              this.$message.error('请填写所有扣除比例');
              return;
            }

            // 定义需要校验的 key
            const requiredKeys = [
              'smallEnterpriseDeductionRate',
              'prisonEnterpriseDeductionRate',
              'disabilityWelfareUnitDeductionRate'
            ];

            // 校验 finalDiscountConfig 是否包含所有必需的 key 且值大于 0
            for (const key of requiredKeys) {
              if (!this.finalDiscountConfig.hasOwnProperty(key) || this.finalDiscountConfig[key] <= 0) {
                this.$message.error(`请确保 ${this.discountConfig.find(item => item.key === key).tname} 大于 0`);
                return;
              }
            }
          }
          //this.getVoToSave()
          let attListForFile = this.showConfigModule ?  []:this.$refs?.baseAttachment?.$children[0]?.attList
          let projectInfoForm = JSON.parse(JSON.stringify(this.projectInfoForm))
          // 如果评审方法为最低价格法 是否评定分离为空字符串
          if(this.projectInfoForm.reviewMethod === '最低价格法') {
            projectInfoForm.isEvaluationSeparation = ''
          }
          this.$callApi(
            "WFSAVE&dataType=ProjectInfoEntity",
            {
              bizid: this.rowId,
              projectInfoVo: projectInfoForm,
              attList: attListForFile,
              demandOrFile: "采购文件",
              discountConfig: { 
                ...this.finalDiscountConfig,
                bizid: this.disBizid,
                enable: this.projectInfoForm.enable
              }
            },
            (result) => {
              if (result.success) {
                callBack();
              }
              return true;
            }
          );
        }
      });
    },

    clearData() {
      this.projectInfoForm = {
        projectInfoName: "",
        biddingNo: "",
        purchaseCategory: "",
        procurementMethodName: "",
        purchaseActuator: "",
        reviewMethod: "",
        agencyName: "",
        isEvaluationSeparation: "否",
        quotationType: "",
        isContractProcessedInSystem: "",
        enable: '是'
      };
      this.isShowEvaluationSeparation = false;
      this.$nextTick(() => {
        this.$refs["infoForm"].clearValidate();
      });
    },

    selectListOpt() {
      this.$callApi("selectDatas", {}, (result) => {
        this.$nextTick(() => {
          //评审方法
          this.reviewProcessOptions = result.data.reviewMethodFuture;
          //报价类型
          this.quotationOptions = result.data.quotedPriceTypeFuture;
        });
        return true;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
