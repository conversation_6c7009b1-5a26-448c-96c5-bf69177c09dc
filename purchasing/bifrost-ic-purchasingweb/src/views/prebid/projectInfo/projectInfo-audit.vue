

<template>
  <div>
    <b-curd ref="curdList" />
    <projectInfo-audit-dialog ref="auditDialogRef" />
    <projectInfo-affirm-dialog ref="affirmDialogRef" />
    <produce-pur-file ref="producePurRef" />
    <pur-project-card-dialog ref="purProjectCardDialogRef" />
  </div>
</template>

  <script>
import ProjectInfoAuditDialog from "./projectInfo-audit-dialog";
import ProjectInfoAffirmDialog from "./projectInfo-affirm-dialog";
import ProducePurFile from "./produce-pur-fileDialog";
import PurProjectCardDialog from "@/components/purchase/pur-project-cardDialog";
export default {
  name: "projectInfo-audit",
  components: {
    ProjectInfoAuditDialog,
    ProducePurFile,
    ProjectInfoAffirmDialog,
    PurProjectCardDialog,
  },
  provide() {
    return {
      dataApiKey: 'pageProjectInfo',
      dataType: 'ProjectInfoEntity',
      pageRoute:this.$getRouter()
    };
  },
  data() {
    return {
      purchaseModeOptions: [],
      treeDepData: [],
      treesetting: {
        check: {
          enable: true,
        },
        data: {
          simpleData: {
            enable: true,
            idKey: "id",
            pIdKey: "parentId",
          },
          key: {
            name: "name",
          },
        },
      },
      depsTreesetting: {
        check: {
          enable: true,
        },
        data: {
          simpleData: {
            enable: true,
            idKey: "id",
            pIdKey: "parentId",
          },
          key: {
            name: "name",
          },
        },
      },
      showEditBidFile: true,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init(initParams) {
      initParams = initParams || {};
      initParams.params = initParams.params || {};
      initParams.params.dataApiKey = "pageProjectInfo";
      initParams.params.queryStatus = "审核";
      initParams.params.pageRoute = "/prebid/projectInfo/projectInfo-audit";
      initParams.hiddenButtons = ["新增", "修改", "删除", "详情"];
      initParams.searchFormNum = "4";
      initParams.callbackRowDblclick = () => {};
      initParams.searchForm = [
        '采购文件审核状态:PROJECT_INFO_STATUS_like:下拉:#全部:"",待采购人确认,采购人确认不通过,审核中,被退回,审核通过,项目终止',
        "采购项目名称:PROJECT_INFO_NAME_like",
        // "采购部门名称:PURCHASE_DEPARTMENT_NAME_in:树:#" +
        //   JSON.stringify(this.treeDepData) +
        //   ":##" +
        //   JSON.stringify(this.depsTreesetting),
        '采购部门名称:PURCHASE_DEPARTMENT_NAME_like:文本',
        '采购标的分类:PURCHASE_CLASSIFICATION_eq:下拉:#全部:"",货物类,服务类,工程类',
        "采购方式:PROCUREMENT_METHOD_NAME_eq:下拉:#" +
          JSON.stringify(this.purchaseModeOptions),
      ];
      initParams.buttons = [
        {
          text: "审核采购文件",
          icon: "el-icon-edit",
          enabledType: "1",
          mainButton: true,
          click: (row) => {
            this.$refs.auditDialogRef.show(
              row,
              "ProjectInfoEntity",
              "审核",
              this.showEditBidFile
            );
          },
        },
        {
          text: "确认采购文件",
          icon: "el-icon-edit",
          enabledType: "1",
          click: (row) => {
            this.$refs.affirmDialogRef.show(
              row,
              "ProjectInfoEntity",
              "审核",
              this.showEditBidFile
            );
          },
        },
        {
          text: "撤回",
          icon: "el-icon-refresh-left",
          enabledType: "1",
          click: (row) => {
            this.withdrawSubmit(row);
          },
        },
        {
          text: "审核记录",
          icon: "el-icon-time",
          enabledType: "1",
          click: (row) => {
            this.$showWfHistory(
              row.getRowData().bizid,
              null,
              null,
              "wfActionProjectInfoEntity"
            );
          },
        },
        {
          text: "采购文件详情",
          icon: "el-icon-tickets",
          enabledType: "1",
          click: (row) => {
            this.$refs.producePurRef.show(
              row,
              "采购文件详情",
              false,
              this.showEditBidFile
            );
          },
        },
        {
          text: "项目卡片",
          icon: "el-icon-bank-card",
          enabledType: "1",
          click: (row) => {
            const rows = row.getRowData();
            if (!rows.bizid && !rows.purProjectInfoBizid) {
              this.$message.warning("暂无项目卡片信息!");
              return false;
            }
            this.$refs.purProjectCardDialogRef.show(
              rows.purProjectInfoBizid || rows.bizid
            );
          },
        },
      ];
      initParams.rowCheckedCallback = async(rows) => {
        if (this.$isNotEmpty(rows)) {
          // 勾选请求是否开启在线制作采购文件
          await this.getInlineProduct(rows[0]?.bizid);
          var projectInfoStatus = rows[0]?.projectInfoStatus;
          this.$call(
            this,
            "basePage",
            "setBtProperty",
            "确认采购文件",
            "disabled",
            projectInfoStatus === "待采购人确认" ? false : true
          );
          this.$call(
            this,
            "basePage",
            "setBtProperty",
            "审核采购文件",
            "disabled",
            projectInfoStatus === "审核中" ? false : true
          );
        }
      };
      //设置从首页跳转过来的待办
      if (this.$isNotEmpty(this.$getSessionParams("pageParameter"))) {
        initParams.isShowDefaultVal =
          "采购文件审核状态:" + this.$getSessionParams("pageParameter");
      }
      //移除首页跳转的待办参数
      this.$removeSessionParams("pageParameter");
      this.$refs.curdList.init(initParams);
    },

    basePageInited(_this) {
      this.selectPurchaseMethod(_this);
      this.getDeptData(_this);
    },

    deptCallback(_this, treeDepData) {
      _this.updateTree &&
        _this.updateTree([
          "采购部门名称:PURCHASE_DEPARTMENT_NAME_in:树:#" +
            JSON.stringify(treeDepData) +
            ":##" +
            JSON.stringify(this.treesetting),
        ]);
    },

    async getDeptData(_this) {
      this.treeDepData = await this.$getDept(_this, this.deptCallback);
    },

    selectPurchaseMethod(_this) {
      this.$callApi("selectPurchaseMethodItem", "", (result) => {
        this.purchaseModeOptions = [];
        result.data.forEach((row) => {
          this.purchaseModeOptions.push({
            value: row.name,
            label: row.name,
          });
        });
        _this.updateTree &&
          _this.updateTree([
            "采购方式:PROCUREMENT_METHOD_NAME_eq:下拉:#" +
              JSON.stringify(this.purchaseModeOptions),
          ]);
        return true;
      });
    },

    reload() {
      this.init();
    },

    withdrawSubmit(row) {
      let params = {
        ids: row.getRowData().ID,
        dataType: "ProjectInfoEntity",
        apiKey: "WFWITHDRAW",
      };
      this.$confirm("确定撤回选中的项目?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$callApiParams("WFWITHDRAW", params, (result) => {
          this.init();
          return true;
        });
      });
    },
    // 获取是否开启在线制作采购文件
    getInlineProduct(bizid = "") {
      let fetchObj = bizid ? { bizid } : {};
      return new Promise((resolve, reject) => {
        this.$callApiParams(
          "getIsEnableOnlineFurFileSysParams",
          fetchObj,
          ({ data }) => {
            const {isEnableOnlineFurFileSysParams } = data
            this.showEditBidFile =
              isEnableOnlineFurFileSysParams !== "否" || isEnableOnlineFurFileSysParams === "是" ? true : false;
            resolve();
            return true;
          },
          (error) => {
            reject(error);
          }
        );
      });
    },
  },
};
</script>
  <style scoped lang='scss'>
</style>
