<template>
    <div>
        <b-curd ref="curdList" />
        <procurement-documents-edit ref="procurementDocumentsEdit" />
        <pur-project-card-dialog ref="purProjectCardDialogRef"/>
    </div>
</template>


<script>
import procurementDocumentsEdit from './procurement-documents-edit'
import PurProjectCardDialog from '@/components/purchase/pur-project-cardDialog'
export default {
    name: 'procurement-documents-chang',
    components: {procurementDocumentsEdit, PurProjectCardDialog},
    mounted() {
        this.init()
    },
    methods: {
        init() {
            var initParams = {}
            initParams.params = {
                dataApiKey: 'pageProcurementDocumentsChang',
                // deleteApiKey: 'deleteProcurementDocumentsChang',
                queryStatus: '变更',
                pageRoute : this.$getRouter()
            }
            initParams.callbackRowDblclick= () => {}
            initParams.searchForm = [
                '变更状态:CHANGE_STATUS_eq:下拉:#全部:"",可变更,未送审,审核中,被退回,审核通过',
                '采购项目名称:PROJECT_INFO_NAME_like:文本',
                '采购部门名称:PURCHASE_DEPARTMENT_NAME_like:文本',
                '招标编号:BIDDING_NO_like:文本'
            ]
            initParams.hiddenButtons = ['新增', '修改', '详情', '删除']
            initParams.buttons = [
                {
                    text: '采购文件变更申请', icon: 'el-icon-plus', enabledType: '1', mainButton: true, click: row => {
                        if("审核中"===row.getRowData().changeStatus||"审核通过"===row.getRowData().changeStatus){
                            this.$message.error('此采购文件不能进行变更！');
                        }else{
                            this.$refs.procurementDocumentsEdit.show(row, '采购文件变更申请')
                        }
                    }
                },
                {
                    text: '删除采购文件变更申请', icon: 'el-icon-delete', enabledType: '1', click: row => {
                        if("未送审"===row.getRowData().changeStatus){
                            this.$callApiParams('isAnnouncementPeriod', {projectInfoBizid:row.getRowData().projectInfoBizid}, result => {
                            if (result.success) {
                                var isAnnouncementPeriod = result.data
                                if(isAnnouncementPeriod){
                                    this.updateProcurementDocuments(row.getRowData().id);
                                }else{
                                    this.deleteProcurementDocuments(row.getRowData().id);
                                }
                                this.init()
                            }
                            return true
                            })
                        }else{
                            this.$message.error('此采购文件不能删除！');
                        }

                    }
                },
                {
                    text: '送审', icon: 'el-icon-s-promotion', enabledType: '1', click: row => {
                        if("未送审"===row.getRowData().changeStatus||"审核不通过"===row.getRowData().changeStatus){
                            let params = {
                            ids:row.getRowData().id,
                            dataType:'ProcurementDocumentsChangEntity',
                            apiKey: 'WFSUBMIT'
                            }
                            this.$confirm('确定送审选中的项目?', '提示', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                                }).then(() => {
                                    this.$callApiParams('WFSUBMIT'
                                    , params, result => {
                                        this.init()
                                    return true
                                })
                            })

                        }else{
                            this.$message.error('此采购文件不能进行送审！');
                        }

                    }
                },
                {
                    text: '撤回', icon: 'el-icon-refresh-left', enabledType: '1', click: row => {
                        let params = {
                            ids:row.getRowData().id,
                            dataType:'ProcurementDocumentsChangEntity',
                            apiKey: 'WFWITHDRAW',
                            wirhdrawType:'变更'
                        }
                        this.$confirm('确定撤回选中的项目?', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                            }).then(() => {
                            this.$callApiParams('WFWITHDRAW'
                            , params, result => {
                                this.init()
                                return true
                            })
                        })
                    }
                },
                {
                    text: '审核记录', icon: 'el-icon-time', enabledType: '1', click: row => {
                        this.$showWfHistory( row.getRowData().id, null, null, 'wfActionProcurementDocumentsChangEntity' )
                    }
                },
                {
                    text: '申请表详情', icon: 'el-icon-tickets', enabledType: '1', click: row => {
                        this.$refs.procurementDocumentsEdit.show(row, '申请表详情')
                    }
                },
                {   text: '项目卡片', icon: 'el-icon-bank-card', enabledType: '1', click: row => {
                    const rows = row.getRowData()
                    if(!rows.projectInfoBizid && !rows.purProjectInfoBizid) {
                        this.$message.warning('暂无项目卡片信息!')
                        return false
                    }
                    this.$refs.purProjectCardDialogRef.show(rows.purProjectInfoBizid ||rows.projectInfoBizid)
                    }
                },
                {
                    text: "导出",
                    enabledType: "0",
                    dropDowns: [
                    {
                        text: "导出当前页",
                        icon: "el-icon-edit",
                        enabledType: "1",
                        click: (bt) =>  this.excelDownload(bt,'current')
                    },
                    {
                        text: "导出所有",
                        icon: "el-icon-edit",
                        enabledType: "1",
                        click: (bt) =>  this.excelDownload(bt,'all')
                    },
                    {
                        text: "导出勾选",
                        icon: "el-icon-edit",
                        enabledType: "1",
                        click: (bt) =>  this.excelDownload(bt,'checked')
                    },
                    ],
                },
            ]
            initParams.rowCheckedCallback= rows => {
                if (this.$isNotEmpty(rows)) {
                var changeStatus = rows[0].changeStatus
                  this.$call(this, 'basePage', 'setBtProperty', '采购文件变更申请', 'disabled', changeStatus=="未送审"||changeStatus=="可变更" || changeStatus=="审核不通过"? false:true)
                  this.$call(this, 'basePage', 'setBtProperty', '删除采购文件变更申请', 'disabled', changeStatus=="未送审"? false:true)
                  this.$call(this, 'basePage', 'setBtProperty', '送审', 'disabled', changeStatus=="未送审"||changeStatus=="审核不通过"? false:true)
                }
            }
            initParams.isShowOrderNumber = true
            this.$refs.curdList.init(initParams)
        },
        updateProcurementDocuments(id){
            let params = Object.assign({
                id:id
            });
            this.$callApiParams('deleteProcurementDocumentsChang', params, (res) => {
                this.$message.success('操作成功!')
                return true
            })
        },
        deleteProcurementDocuments(id){
            let params = {
                ids:id,
                dataType:'ProcurementDocumentsChangEntity',
                apiKey: 'WFDELETE'
            }
            this.$confirm('确定删除选中的项目?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$callApiParams('WFDELETE'
                , params, result => {
                    this.init()
                    return true
                })
            })
        },
        /**
         * 导出
         * @param {*} data 元数据
         * @param {*} type 类型
         * current: 导出当前页
         * all: 导出所有
         * checked: 导出勾选
         **/
        excelDownload(data, type) {
        let params = {
            文件名称: '采购文件变更数据.xls',
            exportExcelName: '采购文件变更数据.xls',
            doExcelExport: true
        };
        const downloadStrategies = {
            current: (params, { params: { baseListObj: { current, size } } }) => {
            params.current = current;
            params.size = size;
            return true;
            },
            all: () => true,
            checked: (params, data, context) => {
            const rowIds = context.$getTableCheckedIdsStrBy(data.params.rows);
            if (rowIds) {
                params.ids = rowIds;
                return true;
            } else {
                context.$message.warning('请勾选要导出的数据！');
                return false;
            }
            }
        };
        const strategy = downloadStrategies[type];
        if (!strategy) {
            this.$message.warning('无效的下载类型');
            return;
        }
        const flag = strategy(params, data, this);
        if (!flag) {
            return;
        }
          params.pageRoute = this.$getRouter();
          params.queryStatus = '变更';
        this.$fileDownloadBykey('exportDocumentsChangFile', params);
        },
    }
}
</script>

