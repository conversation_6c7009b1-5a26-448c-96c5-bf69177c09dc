<template>
  <div>
    <el-dialog
      ref="missingRecordDlg"
      title="缺票提醒记录"
      :visible.sync="dialogShow"
      :close-on-click-modal='false'
      append-to-body
    >
      <el-tabs type="border-card" v-model="activeTab" @tab-click="tabClick" class="tabs-column" style="height: calc(100% - 10px); margin-bottom: 5px">
        <el-tab-pane name="待补充" :label="'待补充('.concat(this.missingCount).concat(')')" style="height: 100%">
          <b-list ref="waitBl"></b-list>
        </el-tab-pane>
        <el-tab-pane name="已补充" :label="'已补充('.concat(this.unMissingCount).concat(')')" style="height: 100%">
          <b-list ref="haveBl"></b-list>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <missing-record-bl ref="missingRecordBl"/>
  </div>
</template>

<script>

export default {
  name: 'audit-billMissing-record',
  components: {},
  data() {
    return {
      dialogShow: false, // 是否显示弹窗
      activeTab: '',
      missingCount: 0,
      unMissingCount: 0
    }
  },
  methods: {
    show() {
      this.activeTab = '待补充'
      this.$setDlgSize(this, 'missingRecordDlg', 960, 630)
      this.dialogShow = true
      this.$nextTick(() => {
        this.initWaitBl()
      })
    },
    initWaitBl() {
      this.$refs.waitBl.init({
        showPager: true,
        noSelection: false,
        orderNumber: { label: '序号', isShow: true },
        params: {
          dataApiKey: 'selectFileMissRecord'
        },
        buttons: [
          { text: '撤回', icon: '', enabledType: '1+', click: row => this.billMissingBack() }
          // { text: '催一催', icon: '', enabledType: '1+' }
        ],
        searchForm: [
          '单据编号:BILL_CODE_like:文本',
          '单据类型:BILL_TYPE_like:文本',
          '经办人:USER_NAME_like:文本'
        ],
        reloadTableCallback: result => {
          if (result.success) {
            this.missingCount = result.attributes.missingCount ? result.attributes.missingCount : 0
            this.unMissingCount = result.attributes.unMissingCount ? result.attributes.unMissingCount : 0
            return true
          }
        }
      })
    },
    initHaveBl() {
      this.$refs.haveBl.init({
        showPager: true,
        noSelection: true,
        orderNumber: { label: '序号', isShow: true },
        params: {
          dataApiKey: 'selectFileMissUploadRecord'
        },
        buttons: [
          { text: '查看附件', icon: '', enabledType: '1', click: row => this.viewFiles(row) }
        ],
        reloadTableCallback: result => {
          if (result.success) {
            return true
          }
        }
      })
    },
    tabClick() {
      if (this.activeTab === '待补充') {
        this.initWaitBl()
      }
      if (this.activeTab === '已补充') {
        this.initHaveBl()
      }
    },
    billMissingBack() {
      var checkedRows = this.$getTableSelection(this.$refs.waitBl.getTable())
      if (checkedRows.length === 0) {
        this.$message.error('请勾选一条数据!')
      } else {
        var ids = { 'ids': this.$getTableCheckedIdsStrBy(checkedRows) }
        this.$callApiParams('billMissingBack', ids,
          result => {
            if (result.success) {
              this.initWaitBl()
            }
          })
      }
    },
    viewFiles(row) {
      this.$nextTick(() => {
        this.$callApiParams('selectBlFiles',
          { bizId: row.getRowValue('bizid') }, result => {
            this.accountBlMissingVo = result.data
            var data = {
              entity: this.accountBlMissingVo.entity,
              attList: this.accountBlMissingVo.attList,
              data: { id: this.accountBlMissingVo.entity.id },
              hiddenCustomizeButton: ['上传附件', '删除', '预览文件']
            }
            this.$refs.missingRecordBl.init(data)
            return true
          })
        return true
      })
    }

  }
}
</script>

<style scoped>

</style>
