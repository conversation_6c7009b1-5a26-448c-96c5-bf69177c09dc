<template>
  <div class="tinymce-editor">
    <Editor
      :id="tinymceId"
      v-model="myValue"
      :init="init"
      @onClick="onClick"
      :disabled="isDisabled"
      ref="editorRef"
    ></Editor>
  </div>
</template>

<script>
import Editor from "@tinymce/tinymce-vue";

export default {
  name: "myTinymce",
  components: {
    Editor,
  },
  props: {
    id: {
      type: String,
      default: function () {
        return 'vue-tinymce-' + +new Date() + ((Math.random() * 1000).toFixed(0) + '')
      },
    },
    value: {
      type: String,
      default: "",
    },
    editHeight: {
      type: String,
      default: '400',
    },
    defaultHeight: {
      type: Number,
      default: 500,
    },
    // 是否禁用
    isDisabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value(newValue) {
      this.$set(this, 'myValue', newValue)
    },
    myValue(newValue) {
      this.myValue = newValue;
      this.$emit("input", newValue);
    },
  },
  data() {
    return {
      tinymceId: this.id,
      //初始化配置
      init: {
        selector: `#${this.tinymceId}`,
        language_url: require("@/assets/tinymce/langs/zh-Hans.js"),
        language: "zh-Hans",
        height: this.defaultHeight,
        // min_height: this.editHeight,
        branding: false,
        // image link
        plugins: [
          "advlist autolink lists   charmap print preview anchor",
          "searchreplace visualblocks code fullscreen",
          "insertdatetime table paste code  wordcount",
        ],
        toolbar:
          "undo redo | formatselect | bold italic backcolor | \
                    alignleft aligncenter alignright alignjustify | \
                    bullist numlist outdent indent | removeformat |  \
                    table",
                    // image |暂时屏蔽图片
        // plugins: this.plugins,
        // toolbar: this.toolbar,
        // min_height: 300,
        // max_height: 300,
        // object_resizing: false, //禁用表格内联样式拖拽拉伸
        // table_resize_bars: false,//禁用表格单元格拖拽拉伸
        //  plugins: this.plugins,
        //  toolbar: this.toolbar,
        // content_style: "p {margin: 5px 0;}", //内容样式
        // fontsize_formats: "12px 14px 16px 18px 24px 36px 48px 56px 72px",
        // font_formats: "宋体='宋体';仿宋='仿宋';微软雅黑='微软雅黑';楷体='楷体';;隶书='隶书';幼圆='幼圆';Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings",
        // branding: false,//不显示富文本支持方
        // branding: false,
        // menubar: false,//禁用菜单栏  如果放开菜单栏，则把这个代码注释
        paste_data_images: true,
        images_upload_handler: (blobInfo, success, failure) => {
          // const img = 'data:image/jpeg;base64,' + blobInfo.base64()
          // success(img)
          let formData = new FormData();
          formData.append("img", blobInfo.blob());
          axios
            .post("http://127.0.0.1:8000/upload/", formData)
            .then((response) => {
              console.log(response.data["url"]);
              if (response.data["code"] == 200) {
                success(response.data["url"]);
              } else {
                failure("上传失败！");
              }
            });
        },
        // resize: false
      },
      myValue: this.myValue,
    };
  },

  mounted() {
    // 初始化保持默认配置 如果传空 会影响二次渲染
    tinymce.init(this.init);
  },

  methods: {
    onClick(e) {
      //alert(this.myValue)
      this.$emit("onClick", e, tinymce);
    },
    //可以添加一些自己的自定义事件，如清空内容
    clear() {
      this.myValue = "";
    },
    setInitVal(val) {
      this.myValue = val;
    },
    setContent(value) {
      window.tinymce.get(this.tinymceId).setContent(value)
    },
    getContent() {
      window.tinymce.get(this.tinymceId).getContent()
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
